# FVM Version Cache
.fvm/

# Firebase local emulator data
.emulator_data/

# Node.js dependencies (should never be committed)
node_modules/
**/node_modules/

# IntelliJ
*.iml
*.ipr
*.iws
.idea/

# Mac
.DS_Store

# Miscellaneous
*.class
*.log
*.pyc
*.swp
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/


# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
/build/
.flutter-plugins
.flutter-plugins-dependencies
.dart_tool/
.pub-cache/
.pub/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Generated buildrunner files
*.g.dart
*.freezed.dart
/.firebase/
*/.firebase/

# Firebase service account keys (sensitive)
**/service-account-key.json
**/firebase-service-account.json
**/*service-account*.json
