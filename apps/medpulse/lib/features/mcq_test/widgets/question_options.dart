import 'package:flutter/material.dart';
import '../../../widgets/cached_image.dart';

/// Widget for displaying MCQ options
class OptionsWidget extends StatelessWidget {
  final List<String> options;
  final List<String> optionImages;
  final List<double?> optionImagesWidth;
  final List<double?> optionImagesHeight;
  final int? selectedIndex;
  final Function(int) onSelect;

  const OptionsWidget({
    super.key,
    required this.options,
    required this.optionImages,
    required this.optionImagesWidth,
    required this.optionImagesHeight,
    this.selectedIndex,
    required this.onSelect,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: options.length,
      itemBuilder: (context, index) {
        final isSelected = selectedIndex == index;
        final optionLabel = String.fromCharCode(65 + index); // A, B, C, D...

        return Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: InkWell(
            onTap: () => onSelect(index),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.outline,
                  width: isSelected ? 2 : 1,
                ),
                color: isSelected
                    ? Theme.of(context).colorScheme.primaryContainer
                    : Theme.of(context).colorScheme.surface,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 28,
                    height: 28,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.surfaceContainerHighest,
                    ),
                    child: Center(
                      child: Text(
                        optionLabel,
                        style: TextStyle(
                          color: isSelected
                              ? Theme.of(context).colorScheme.onPrimary
                              : Theme.of(context).colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          options[index],
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                        // Option image if available
                        if (index < optionImages.length && optionImages[index].isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: CachedImage(
                              key: ValueKey('option-image-$index'),
                              imageWidth: optionImagesWidth[index],
                              imageHeight: optionImagesHeight[index],
                              storagePath: optionImages[index],
                              errorWidget: const SizedBox.shrink(),
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}