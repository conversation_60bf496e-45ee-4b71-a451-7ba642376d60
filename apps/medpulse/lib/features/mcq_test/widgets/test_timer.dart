import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Widget for displaying timer in the test screen
class TestTimerWidget extends ConsumerStatefulWidget {
  final DateTime startTime;
  final bool isCompleted;

  const TestTimerWidget({
    Key? key,
    required this.startTime,
    this.isCompleted = false,
  }) : super(key: key);

  @override
  ConsumerState<TestTimerWidget> createState() => _TestTimerWidgetState();
}

class _TestTimerWidgetState extends ConsumerState<TestTimerWidget> {
  late DateTime _startTime;
  Duration _elapsed = Duration.zero;

  @override
  void initState() {
    super.initState();
    _startTime = widget.startTime;
    _startTimer();
  }

  @override
  void didUpdateWidget(TestTimerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // If the test was just completed, update the UI one last time
    if (widget.isCompleted && !oldWidget.isCompleted) {
      setState(() {
        _elapsed = DateTime.now().difference(_startTime);
      });
    }
  }

  void _startTimer() {
    // Don't start a new timer if the test is completed
    if (widget.isCompleted) {
      return;
    }

    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        // Check again if the test is completed before updating
        if (widget.isCompleted) {
          return;
        }

        setState(() {
          _elapsed = DateTime.now().difference(_startTime);
        });
        _startTimer();
      }
    });
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$hours:$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 16.0),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.timer_outlined, size: 18),
          const SizedBox(width: 4),
          Text(_formatDuration(_elapsed)),
        ],
      ),
    );
  }
}