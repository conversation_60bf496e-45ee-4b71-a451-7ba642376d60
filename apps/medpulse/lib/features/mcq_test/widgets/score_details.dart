import 'package:flutter/material.dart';
import 'package:entities/test_result.dart';

/// Widget displaying detailed test score information
class ScoreDetails extends StatelessWidget {
  final TestResult testResult;

  const ScoreDetails({
    super.key,
    required this.testResult,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          _buildScoreRow(
            context,
            'Score',
            '${testResult.scorePercentage.toStringAsFixed(1)}%',
          ),
          const Divider(),
          _buildScoreRow(
            context,
            'Correct Answers',
            '${testResult.correctAnswers} / ${testResult.questionCount}',
          ),
          const Divider(),
          _buildScoreRow(
            context,
            'Completion',
            '${(testResult.answeredCount / testResult.questionCount * 100).toStringAsFixed(1)}%',
          ),
          const Divider(),
          _buildScoreRow(
            context,
            'Time Taken',
            _formatDuration(testResult.timeTaken),
          ),
        ],
      ),
    );
  }

  Widget _buildScoreRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    final parts = <String>[];
    if (hours > 0) {
      parts.add('$hours hr');
    }
    if (minutes > 0) {
      parts.add('$minutes min');
    }
    if (seconds > 0 || parts.isEmpty) {
      parts.add('$seconds sec');
    }

    return parts.join(' ');
  }
}