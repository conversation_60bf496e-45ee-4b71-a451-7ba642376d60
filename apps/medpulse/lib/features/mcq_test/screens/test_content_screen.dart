import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/common.dart';
import 'package:entities/question_entity.dart';
import 'package:entities/test_progress.dart';
import '../../../providers/question_provider.dart';
import '../../../providers/test_provider.dart';
import '../../../theme/app_theme.dart';
import '../providers/test_progress_provider.dart';
import '../../../providers/user_provider.dart';
import '../services/question_report_service.dart';
import '../../../widgets/cached_image.dart';
import '../../../widgets/upgrade_dialog.dart';
import '../widgets/question_navigation_bar.dart';
import '../widgets/question_view.dart';
import '../widgets/test_options_menu.dart';
import '../widgets/report_question_dialog.dart';

/// Widget displaying the main test content
class TestContentScreen extends ConsumerWidget {
  final String subscriptionId;
  final String testId;
  final bool free;
  final int questionCount;
  final int fullQuestionCount;
  final QuestionType questionType;

  const TestContentScreen({
    super.key,
    required this.subscriptionId,
    required this.testId,
    required this.free,
    required this.questionCount,
    required this.fullQuestionCount,
    required this.questionType,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final progressAsync = ref.watch(testProgressNotifierProvider(subscriptionId, testId, free: free));
    final isSubscribed = ref.watch(userProvider.notifier).isSubscribedTo(testId);

    return progressAsync.when(
      data: (progress) {
        final selectedAnswer = progress.userAnswers[progress.currentIndex];
        final isMarkedForReview = progress.markedForReview.contains(progress.currentIndex);
        final isBookmarked = ref.read(userProvider.notifier).isQuestionBookmarked(testId, progress.currentIndex);

        return Center(
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 600),
            child: Column(
              children: [
                // Progress indicator
                LinearProgressIndicator(value: (progress.currentIndex + 1) / questionCount),

                // Question navigation
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: QuestionNavigationBar(
                    testId: testId,
                    free: free,
                    questionCount: questionCount,
                    fullQuestionCount: fullQuestionCount,
                    currentIndex: progress.currentIndex,
                    userAnswers: progress.userAnswers,
                    markedForReview: progress.markedForReview,
                    bookmarkedQuestions: progress.bookmarkedQuestions,
                    onTap: (index) {
                      ref
                          .read(testProgressNotifierProvider(subscriptionId, testId, free: free).notifier)
                          .goToQuestion(index, questionCount);
                    },
                    onOptionsPressed:
                        () => _showOptionsMenu(
                          context,
                          ref,
                          progress.currentIndex,
                          isMarkedForReview,
                          isBookmarked,
                          isSubscribed,
                        ),
                  ),
                ),

                // Question content
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: QuestionView(
                        subscriptionId: subscriptionId,
                        testId: testId,
                        free: free,
                        questionIndex: progress.currentIndex,
                        selectedAnswer: selectedAnswer,
                        questionType: questionType,
                        onSelect: (index) {
                          final success = ref
                              .read(testProgressNotifierProvider(subscriptionId, testId, free: free).notifier)
                              .selectAnswer(index);
                          if (!success) {
                            // If the answer couldn't be selected (e.g., already revealed), show a dialog
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('You cannot answer a question after revealing its answer.'),
                                duration: Duration(seconds: 3),
                              ),
                            );
                          }
                        },
                      ),
                    ),
                  ),
                ),

                // Navigation buttons
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Previous button
                      PreviousButton(
                        onPressed:
                            progress.currentIndex > 0
                                ? () =>
                                    ref
                                        .read(testProgressNotifierProvider(subscriptionId, testId, free: free).notifier)
                                        .goToPreviousQuestion()
                                : null,
                      ),

                      /*
                  // Options button is in the top right menu now.
                  IconButton(
                    icon: const Icon(Icons.more_vert),
                    onPressed: () => _showOptionsMenu(
                      context,
                      ref,
                      progress.currentIndex,
                      isMarkedForReview,
                      isBookmarked,
                      isSubscribed,
                    ),
                    tooltip: 'Test options',
                  ),
*/

                      // Navigation actions
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // "Mark for review" quick action
                          IconButton(
                            onPressed: () => _toggleMarkForReview(ref, progress.currentIndex),
                            icon: Icon(
                              isMarkedForReview ? Icons.flag : Icons.flag_outlined,
                              color: getAppColor(context).markedForReviewBackground,
                            ),
                            tooltip: isMarkedForReview ? 'Unmark for review' : 'Mark for review',
                          ),

                          // Show Reveal Answer button before Next button
                          ...[
                            RevealButton(
                              onPressed: () {
                                // Get the current question to determine its type
                                final questionAsync = ref.read(
                                  questionProvider(subscriptionId, testId, progress.currentIndex, free: free),
                                );
                                questionAsync.whenData((question) {
                                  final isMcqQuestion = question.type == QuestionType.mcq;

                                  // For MCQ questions, check if already revealed and show confirmation
                                  if (isMcqQuestion) {
                                    final isRevealed = ref
                                        .read(testProgressNotifierProvider(subscriptionId, testId, free: free).notifier)
                                        .isAnswerRevealed(progress.currentIndex);

                                    if (isRevealed) {
                                      // If already revealed, just show the answer again
                                      _showAnswerDialog(context, question, null);
                                    } else {
                                      // Show confirmation dialog before revealing MCQ answers
                                      _showRevealConfirmationDialog(context, ref, progress);
                                    }
                                  } else {
                                    // For flip cards, just show the answer directly without confirmation
                                    // and allow answering again (pass the onSelect callback)
                                    _showAnswerDialog(context, question, (answerValue) {
                                      ref
                                          .read(testProgressNotifierProvider(subscriptionId, testId, free: free).notifier)
                                          .selectAnswer(answerValue);
                                    });
                                  }
                                });
                              },
                            ),
                            const SizedBox(width: 8),
                          ],

                          // Next/Finish button
                          NextButton(
                            onPressed: () {
                              if (progress.currentIndex < questionCount - 1) {
                                // Go to next question
                                ref
                                    .read(testProgressNotifierProvider(subscriptionId, testId, free: free).notifier)
                                    .goToNextQuestion(questionCount);
                              } else if (free && fullQuestionCount > questionCount) {
                                // For free tests with more premium questions, show upgrade dialog
                                final remainingQuestions = fullQuestionCount - questionCount;
                                showUpgradeDialog(
                                  context,
                                  ref,
                                  'You have completed all $questionCount free questions in this test.',
                                  scenario: UpgradeScenario.freeTestEnd,
                                  remainingQuestions: remainingQuestions,
                                  onFinishTest: () => _showFinishConfirmation(context, ref, progress),
                                );
                              } else {
                                // For premium tests or free tests with no additional premium questions
                                _showFinishConfirmation(context, ref, progress);
                              }
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) {
        dbgPrint(stackTrace.toString());
        return Center(child: Text('Error: ${error.toString()}'));
      },
    );
  }

  void _showFinishConfirmation(BuildContext context, WidgetRef ref, TestProgress progress) {
    final answeredCount = progress.userAnswers.length;
    final markedCount = progress.markedForReview.length;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Finish Test?'),
            content: Text(
              'You have answered $answeredCount out of $questionCount questions'
              '${markedCount > 0 ? ' and have $markedCount questions marked for review' : ''}. '
              'Are you sure you want to finish the test?',
            ),
            actions: [
              TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Continue Test')),
              FilledButton(
                onPressed: () {
                  Navigator.of(context).pop(); // Close dialog
                  ref.read(testProgressNotifierProvider(subscriptionId, testId, free: free).notifier).completeTest(questionCount);
                },
                child: const Text('Finish Test'),
              ),
            ],
          ),
    );
  }

  // Show confirmation dialog before revealing an MCQ answer
  void _showRevealConfirmationDialog(BuildContext context, WidgetRef ref, TestProgress progress) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Reveal MCQ Answer?'),
            content: const Text(
              'If you reveal the answer to this question, you will not be able to change your current answer any more.\n\n'
              'Are you sure you want to reveal the answer?',
            ),
            actions: [
              TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel')),
              FilledButton(
                onPressed: () {
                  Navigator.of(context).pop();

                  // Mark the answer as revealed
                  ref
                      .read(testProgressNotifierProvider(subscriptionId, testId, free: free).notifier)
                      .revealAnswer(progress.currentIndex);

                  // Get the current question and show the answer
                  final questionAsync = ref.read(questionProvider(subscriptionId, testId, progress.currentIndex, free: free));
                  questionAsync.whenData((question) {
                    _showAnswerDialog(context, question, null);
                  });
                },
                child: const Text('Reveal Answer'),
              ),
            ],
          ),
    );
  }

  void _showAnswerDialog(BuildContext context, QuestionEntity question, Function(int)? onSelect) {
    final isMcqQuestion = question.type == QuestionType.mcq;
    final isFlipCardQuestion = question.type == QuestionType.flipCard;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          /*
          title: Text(
            'Answer',
            style: Theme.of(context).textTheme.titleLarge,
          ),
*/
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // For MCQ questions, show the correct option
                if (isMcqQuestion && question.options.isNotEmpty) ...[
                  Text(
                    'Answer:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  if (question.correctOptionIndex >= 0 && question.correctOptionIndex < question.options.length)
                    Text(question.options[question.correctOptionIndex], style: Theme.of(context).textTheme.bodyLarge),
                ],

                // For flip card questions, show the answer text
                if (isFlipCardQuestion && question.answer != null && question.answer!.isNotEmpty) ...[
                  Text(
                    'Answer:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(question.answer!, style: Theme.of(context).textTheme.bodyLarge),
                ],

                // Answer image if available
                if (question.answerImage != null && question.answerImage!.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Center(
                      child: CachedImage(
                        key: ValueKey('answer-view-${question.answerImage!}'),
                        imageWidth: question.answerImageWidth,
                        imageHeight: question.answerImageHeight,
                        storagePath: question.answerImage!,
                        errorWidget: const Icon(Icons.image_not_supported, size: 64),
                      ),
                    ),
                  ),

                // Explanation if available
                if (question.explanation != null && question.explanation!.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Text(
                    'Explanation:',
                    style: Theme.of(
                      context,
                    ).textTheme.titleMedium?.copyWith(color: Theme.of(context).colorScheme.primary),
                  ),
                  const SizedBox(height: 8),
                  Text(question.explanation!, style: Theme.of(context).textTheme.bodyMedium),
                ],

                // Reference if available
                if (question.reference != null && question.reference!.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Text(
                    'Reference:',
                    style: Theme.of(
                      context,
                    ).textTheme.titleMedium?.copyWith(color: Theme.of(context).colorScheme.primary),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    question.reference!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(fontStyle: FontStyle.italic),
                  ),
                ],
              ],
            ),
          ),
          actions:
              isFlipCardQuestion
                  ? [
                    // I got it wrong button
                    OutlinedButton(
                      onPressed: () {
                        Navigator.of(dialogContext).pop();
                        onSelect?.call(2); // 2 means wrong answer
                      },
                      style: OutlinedButton.styleFrom(
                        foregroundColor: getAppColor(context).incorrectAnswerColor,
                        padding: const EdgeInsets.all(12),
                        shape: const CircleBorder(),
                      ),
                      child: Tooltip(
                        message: 'I got it wrong',
                        child: Icon(Icons.close, color: getAppColor(context).incorrectAnswerColor, size: 28),
                      ),
                    ),
                    // I got it right button
                    FilledButton(
                      onPressed: () {
                        Navigator.of(dialogContext).pop();
                        onSelect?.call(1); // 1 means correct answer
                      },
                      style: FilledButton.styleFrom(
                        backgroundColor: getAppColor(context).correctAnswerColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.all(12),
                        shape: const CircleBorder(),
                      ),
                      child: const Tooltip(message: 'I got it right', child: Icon(Icons.check, size: 28)),
                    ),
                  ]
                  : [
                    // For MCQ questions, show an OK button on the right
                    const Spacer(), // Push the button to the right
                    FilledButton(onPressed: () => Navigator.of(dialogContext).pop(), child: const Text('OK')),
                  ],
          actionsPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          actionsAlignment: MainAxisAlignment.spaceBetween,
        );
      },
    );
  }

  void _showOptionsMenu(
    BuildContext context,
    WidgetRef ref,
    int currentQuestionIndex,
    bool isMarkedForReview,
    bool isBookmarked,
    bool isPremiumUser,
  ) {
    TestOptionsMenu.show(
      context: context,
      ref: ref,
      currentQuestionIndex: currentQuestionIndex,
      isMarkedForReview: isMarkedForReview,
      isBookmarked: isBookmarked,
      isPremiumUser: isPremiumUser,
      onFinishTest:
          () =>
              _showFinishConfirmation(context, ref, ref.read(testProgressNotifierProvider(subscriptionId, testId, free: free)).value!),
      onToggleMarkForReview: () => _toggleMarkForReview(ref, currentQuestionIndex),
      onToggleBookmark: () => _toggleBookmark(ref, currentQuestionIndex),
      onReportQuestion: () => _showReportDialog(context, ref, currentQuestionIndex),
    );
  }

  void _toggleMarkForReview(WidgetRef ref, int questionIndex) {
    ref.read(testProgressNotifierProvider(subscriptionId, testId, free: free).notifier).toggleMarkedForReview(questionIndex);
  }

  Future<void> _toggleBookmark(WidgetRef ref, int questionIndex) async {
    // Update local test progress
    ref.read(testProgressNotifierProvider(subscriptionId, testId, free: free).notifier).toggleBookmarked(questionIndex);

    // Get the published test to extract required metadata
    final publishedTestAsync = ref.read(testProvider(subscriptionId, testId, free: false));

    if (publishedTestAsync.hasError || publishedTestAsync.isLoading) {
      return;
    }

    final publishedTest = publishedTestAsync.valueOrNull;
    if (publishedTest == null) return;

    // Sync with user profile using the new user provider method
    await ref
        .read(userProvider.notifier)
        .toggleQuestionBookmark(
          testId,
          questionIndex,
          publishedTest.courseId,
          publishedTest.subjectId,
          publishedTest.subscriptionId,
          publishedTest.version,
        );
  }

  void _showReportDialog(BuildContext context, WidgetRef ref, int questionIndex) {
    final reportService = QuestionReportService();

    // Use a separate async function to avoid BuildContext across async gaps
    _fetchDataAndShowReportDialog(context, ref, questionIndex, reportService);
  }

  // Separate async function to handle data fetching
  Future<void> _fetchDataAndShowReportDialog(
    BuildContext context,
    WidgetRef ref,
    int questionIndex,
    QuestionReportService reportService
  ) async {
    // Check if context is still valid
    if (!context.mounted) return;

    try {
      // Get the test to extract version, courseId, and yearId
      final testAsync = await ref.read(testProvider(subscriptionId, testId, free: free).future);
      final version = testAsync.version;
      final courseId = testAsync.courseId;
      final yearId = testAsync.yearId;

      // Get the question data
      final questionAsync = await ref.read(questionProvider(subscriptionId, testId, questionIndex, free: free).future);
      final questionData = questionAsync.toJson();

      // Check if context is still valid after async operations
      if (!context.mounted) return;

      ReportQuestionDialog.show(
        context: context,
        testId: testId,
        questionIndex: questionIndex,
        onSubmit: (testId, questionIndex, issueType, comments) async {
          // Submit the report with additional information
          await reportService.reportQuestion(
            testId: testId,
            questionIndex: questionIndex,
            issueType: issueType,
            comments: comments,
            version: version,
            courseId: courseId,
            yearId: yearId,
            questionData: questionData,
          );
        },
      );
    } catch (e) {
      // Handle any errors that might occur during data fetching
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error preparing report: ${e.toString()}'))
        );
      }
    }
  }
}

/// A dedicated widget for the reveal answer button that adapts to screen size
class RevealButton extends StatelessWidget {
  final VoidCallback onPressed;

  const RevealButton({super.key, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.sizeOf(context).width;
    final isSmallScreen = screenWidth < 400;

    return isSmallScreen
        ? IconButton.outlined(onPressed: onPressed, icon: const Icon(Icons.visibility), tooltip: 'Reveal Answer')
        : OutlinedButton.icon(onPressed: onPressed, icon: const Icon(Icons.visibility), label: const Text('Reveal'));
  }
}

/// A dedicated widget for the next/finish button that adapts to screen size
class NextButton extends StatelessWidget {
  final VoidCallback onPressed;

  const NextButton({super.key, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.sizeOf(context).width;
    final isSmallScreen = screenWidth < 400;

    return isSmallScreen
        ? IconButton.filled(onPressed: onPressed, icon: const Icon(Icons.arrow_forward), tooltip: 'Next')
        : FilledButton.icon(onPressed: onPressed, icon: const Icon(Icons.arrow_forward), label: const Text('Next'));
  }
}

/// A dedicated widget for the previous button that adapts to screen size
class PreviousButton extends StatelessWidget {
  final VoidCallback? onPressed;

  const PreviousButton({super.key, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.sizeOf(context).width;
    final isSmallScreen = screenWidth < 400;

    return isSmallScreen
        ? IconButton.outlined(onPressed: onPressed, icon: const Icon(Icons.arrow_back), tooltip: 'Previous')
        : ElevatedButton.icon(onPressed: onPressed, icon: const Icon(Icons.arrow_back), label: const Text('Previous'));
  }
}
