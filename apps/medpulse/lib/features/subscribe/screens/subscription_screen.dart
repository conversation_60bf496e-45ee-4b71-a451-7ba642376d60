import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/features/subscribe/screens/benefits_screen.dart';
import 'package:medpulse/features/subscribe/screens/plans_screen.dart';
import 'package:medpulse/features/subscribe/screens/authentication_screen.dart';
import 'package:medpulse/features/subscribe/screens/order_summary_screen.dart';
import 'package:medpulse/features/subscribe/screens/payment_screen.dart';
import 'package:medpulse/features/subscribe/screens/confirmation_screen.dart';
import 'package:medpulse/providers/firebase_user_provider.dart';
import 'package:medpulse/providers/user_provider.dart';
import 'package:medpulse/widgets/constrained_width_app_bar.dart';
import 'package:medpulse/widgets/legal_documents_dialog.dart';

/// Enum representing the different pages in the subscription flow
enum SubscriptionPage {
  benefits,
  plans,
  authentication,
  orderSummary,
  payment,
  confirmation,
}

/// Main subscription screen that manages the flow between different subscription steps
class SubscriptionScreen extends ConsumerStatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  ConsumerState<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends ConsumerState<SubscriptionScreen> {
  final PageController _pageController = PageController();
  SubscriptionPage _currentPage = SubscriptionPage.benefits;
  String? _selectedPlanType;
  String? _transactionId;
  bool _paymentSuccess = false;
  String? _paymentError;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _navigateToPage(SubscriptionPage page) {
    _pageController.animateToPage(
      page.index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
    setState(() {
      _currentPage = page;
    });
  }

  Future<void> _handlePlanSelection(String planType) async {
    setState(() {
      _selectedPlanType = planType;
    });

    // Check if the user is anonymous
    final firebaseUser = ref.read(firebaseUserProvider).valueOrNull;
    if (firebaseUser != null && firebaseUser.isAnonymous) {
      // Navigate to the authentication page
      _navigateToPage(SubscriptionPage.authentication);
      return;
    }

    // Check if the user has accepted the legal documents
    final user = ref.read(userProvider).valueOrNull;
    if (user != null) {
      // Check if the user has accepted the legal documents
      final hasAccepted = ref.read(userProvider.notifier).hasAcceptedLegalDocuments();
      if (!hasAccepted) {
        // Show the legal documents dialog
        final accepted = await LegalDocumentsDialog.show(context: context);
        if (accepted != true || !mounted) return;
      }
    }

    // Navigate to the order summary page
    _navigateToPage(SubscriptionPage.orderSummary);
  }

  void _handleAuthenticationComplete() {
    // Navigate to the order summary page after authentication
    _navigateToPage(SubscriptionPage.orderSummary);
  }

  void _handlePaymentComplete({
    required bool success,
    String? transactionId,
    String? error,
  }) {
    setState(() {
      _paymentSuccess = success;
      _transactionId = transactionId;
      _paymentError = error;
    });

    // Navigate to the confirmation page
    _navigateToPage(SubscriptionPage.confirmation);
  }

  void _handleProceedToPayment() {
    // Navigate to the payment page
    _navigateToPage(SubscriptionPage.payment);
  }

  void _handleChangePlan() {
    // Navigate back to the plans page
    _navigateToPage(SubscriptionPage.plans);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ConstrainedWidthAppBar(
        appBar: AppBar(
          title: const Text('Upgrade to Premium'),
          leading: _currentPage == SubscriptionPage.benefits
              ? IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                )
              : IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () {
                    // Handle back navigation based on current page
                    switch (_currentPage) {
                      case SubscriptionPage.plans:
                        _navigateToPage(SubscriptionPage.benefits);
                        break;
                      case SubscriptionPage.authentication:
                        _navigateToPage(SubscriptionPage.plans);
                        break;
                      case SubscriptionPage.orderSummary:
                        _navigateToPage(SubscriptionPage.plans);
                        break;
                      case SubscriptionPage.payment:
                        _navigateToPage(SubscriptionPage.orderSummary);
                        break;
                      case SubscriptionPage.confirmation:
                        // Don't allow back navigation from confirmation
                        break;
                      default:
                        // Default case should not happen
                        break;
                    }
                  },
                ),
        ),
      ),
      body: PageView(
        controller: _pageController,
        physics: const NeverScrollableScrollPhysics(), // Disable swiping
        children: [
          // Benefits Screen
          BenefitsScreen(
            onContinue: () => _navigateToPage(SubscriptionPage.plans),
          ),

          // Plans Screen
          PlansScreen(
            onPlanSelected: _handlePlanSelection,
          ),

          // Authentication Screen
          AuthenticationScreen(
            onAuthenticationComplete: _handleAuthenticationComplete,
          ),

          // Order Summary Screen
          OrderSummaryScreen(
            planType: _selectedPlanType ?? '',
            onProceedToPayment: _handleProceedToPayment,
            onChangePlan: _handleChangePlan,
          ),

          // Payment Screen
          PaymentScreen(
            planType: _selectedPlanType ?? '',
            onPaymentComplete: _handlePaymentComplete,
          ),

          // Confirmation Screen
          ConfirmationScreen(
            success: _paymentSuccess,
            transactionId: _transactionId,
            error: _paymentError,
            onClose: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
}
