import 'package:entities/question_entity.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../common/string_util.dart';
import 'test_provider.dart';

part 'question_provider.g.dart';

/// Provider for a specific question
@riverpod
Future<QuestionEntity> question(Ref ref, String testId, int questionIndex, {int? version, bool free = true}) async {
  // First, wait for the secure test to load
  final test = await ref.watch(testProvider(testId, version: version, free: free).future);

  // Check if the question index is valid
  if (questionIndex < 0 || questionIndex >= test.questions.length) {
    throw Exception('Invalid question index: $questionIndex. Test has ${test.questions.length} questions.');
  }

  // Get the encrypted question
  final encryptedQuestion = test.questions[questionIndex];

  // Generate the encryption key using only the original test ID (not the full format)
  // Extract the original test ID from the full format: courseId:yearId:originalTestId
  final parts = testId.split(':');
  final originalTestId = parts.length == 3 ? parts[2] : testId;
  final key = StringUtil.gk(originalTestId);

  // Decrypt the question
  return encryptedQuestion.copyWith(
    // Decrypt all string fields
    question: StringUtil.dc(encryptedQuestion.question, key)!,
    answer: StringUtil.dc(encryptedQuestion.answer, key),
    explanation: StringUtil.dc(encryptedQuestion.explanation, key),
    reference: StringUtil.dc(encryptedQuestion.reference, key),
    // Decrypt all strings in lists
    options: encryptedQuestion.options.map((option) => StringUtil.dc(option, key)!).toList(),
    optionImages: encryptedQuestion.optionImages.map((image) => StringUtil.dc(image, key)!).toList(),
    keywords: encryptedQuestion.keywords.map((keyword) => StringUtil.dc(keyword, key)!).toList(),
    // Image URLs
    questionImage: StringUtil.dc(encryptedQuestion.questionImage, key),
    answerImage: StringUtil.dc(encryptedQuestion.answerImage, key),
  );
}
