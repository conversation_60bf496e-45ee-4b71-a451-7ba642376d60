import 'package:entities/published_test_entity.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/cached_doc_provider.dart';
import 'package:providers/common.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'test_provider.g.dart';

/// Provider for fetching published tests
@riverpod
Future<PublishedTestEntity> test(Ref ref, String testId, {int? version, bool free = true}) async {

  // Extract subscription ID from test ID - must be in format courseId:yearId:testId
  final parts = testId.split(':');
  if (parts.length != 3) {
    throw Exception('testProvider: invalid test ID format. Expected courseId:yearId:testId, got: $testId');
  }

  final subscriptionId = '${parts[0]}:${parts[1]}';
  final actualTestId = parts[2];

  // Use the nested path structure: collection/subscriptionId/tests/testId
  final collection = free ? 'published_free' : 'published_tests';
  final docId = version != null ? '$actualTestId.$version' : actualTestId;
  final path = '$collection/$subscriptionId/tests/$docId';

  final json = await ref.watch(cachedDocProvider(path).future);
  if (json == null) {
    final msg = 'testProvider: could not load ${free ? "free" : "premium"} test $testId at path $path';
    dbgPrint(msg);
    throw (Exception(msg));
  }

  // Parse the document into a PublishedTestEntity
  return PublishedTestEntity.fromJson(json);
}

