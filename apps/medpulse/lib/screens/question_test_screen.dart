import 'package:entities/question_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/screens/error_screen.dart';
import 'package:providers/common.dart';
import '../providers/test_provider.dart';
import '../features/mcq_test/providers/test_progress_provider.dart';
import '../features/mcq_test/screens/test_content_screen.dart';
import '../features/mcq_test/widgets/test_results.dart';
import '../features/mcq_test/widgets/test_timer.dart';
import '../services/error_reporting_service.dart';
import '../widgets/constrained_width_app_bar.dart';

/// Main screen for taking a test (MCQ or FlipCard)
class QuestionTestScreen extends ConsumerWidget {
  final String subscriptionId;
  final String testId;
  final String name;
  final bool free;
  final QuestionType questionType;

  const QuestionTestScreen({
    super.key,
    required this.subscriptionId,
    required this.testId,
    required this.name,
    this.free = true,
    required this.questionType,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the test info to get the question count
    final testAsync = ref.watch(testProvider(subscriptionId, testId, free: free));
    final progressAsync = ref.watch(testProgressNotifierProvider(subscriptionId, testId, free: free));

    return Scaffold(
      appBar: ConstrainedWidthAppBar(
        appBar: AppBar(
          title: Text(name),
          leading: IconButton(icon: const Icon(Icons.close), onPressed: () => _showExitConfirmation(context)),
          actions: [
            progressAsync.maybeWhen(
              data: (progress) => TestTimerWidget(startTime: progress.startTime, isCompleted: progress.isCompleted),
              orElse: () => const SizedBox.shrink(),
            ),
          ],
        ),
      ),
      body: testAsync.when(
        data: (test) {
          // Get the question count from the test entity
          final questionCount = test.questionCount;

          return progressAsync.when(
            data: (progress) {
              // If test is completed, show results screen
              if (progress.isCompleted) {
                return TestResultsScreen(
                  subscriptionId: subscriptionId,
                  testId: testId,
                  free: free,
                  progress: progress,
                  questionCount: questionCount,
                );
              }

              // Otherwise show the test screen
              return TestContentScreen(
                subscriptionId: subscriptionId,
                testId: testId,
                free: free,
                questionCount: test.questionCount,
                fullQuestionCount: test.fullQuestionCount,
                questionType: questionType,
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stackTrace) {
              reportError(error, stackTrace);
              dbgPrint(stackTrace.toString());
              return ErrorScreen(
                context: context,
                description: 'Loading test failed.\n${error.toString()}',
                onPressed: () {
                  ref.invalidate(testProgressNotifierProvider(subscriptionId, testId, free: free));
                },
              );
            },
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) {
          reportError(error, stackTrace);
          dbgPrint(stackTrace.toString());
          return ErrorScreen(
            context: context,
            description: 'Test Progress failed.\n${error.toString()}',
            onPressed: () {
              ref.invalidate(testProvider(subscriptionId, testId, free: free));
            },
          );
        },
      ),
    );
  }

  void _showExitConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Exit Test?'),
            content: const Text('Are you sure you want to exit? Your progress will be lost.'),
            actions: [
              TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel')),
              FilledButton(
                onPressed: () {
                  Navigator.of(context).pop(); // Close dialog
                  Navigator.of(context).pop(); // Exit test
                },
                child: const Text('Exit'),
              ),
            ],
          ),
    );
  }
}
