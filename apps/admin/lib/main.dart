// apps/admin/lib/main.dart

import 'package:admin/shared/providers/app_router_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/connectivity_provider.dart';
import 'package:providers/firebase_app_provider.dart';
import 'package:providers/scaffold_messenger_key_provider.dart';

import 'config/theme.dart';
import 'firebase_options.dart';
import 'shared/widgets/error_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  ProvidersFirebaseOptions(DefaultFirebaseOptions.currentPlatform);

  runApp(const ProviderScope(child: AdminPanelApp()));
}

class AdminPanelApp extends ConsumerWidget {
  const AdminPanelApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final firebaseAppState = ref.watch(firebaseAppProvider);
    final isConnected = ref.watch(connectivityProvider).valueOrNull ?? false;
    final scaffoldKey = ref.watch(scaffoldMessengerKeyProvider);

    if (firebaseAppState.isLoading) {
      return MaterialApp(
        theme: AppTheme.lightTheme,
        scaffoldMessengerKey: scaffoldKey,
        home: const Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    if (firebaseAppState.hasError) {
      debugPrint('Firebase initialization error: ${firebaseAppState.error}');
      return MaterialApp(
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        scaffoldMessengerKey: scaffoldKey,
        home: ErrorScreen(
          title: 'Connection Issue',
          message: 'We\'re having trouble connecting to our services. Please check your connection and try again.',
          onRetry: () {
            ref.invalidate(firebaseAppProvider);
          },
          icon: Icons.cloud_off,
        ),
      );
    }

    if (!isConnected) {
      return MaterialApp(
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        scaffoldMessengerKey: scaffoldKey,
        home: ErrorScreen(
          title: 'No Internet Connection',
          message:
              'This app requires an internet connection to function properly. Please check your connection and try again.',
          onRetry: () {
            ref.invalidate(connectivityProvider);
          },
          icon: Icons.wifi_off_rounded,
        ),
      );
    }

    return MaterialApp.router(
      title: 'Medpulse Admin',
      theme: AppTheme.lightTheme,
      scaffoldMessengerKey: scaffoldKey,
      routerConfig: ref.watch(appRouterProvider),
      debugShowCheckedModeBanner: false,
    );
  }
}
