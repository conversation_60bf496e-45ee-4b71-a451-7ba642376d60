// lib/features/dashboard/screens/dashboard_screen.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/login_provider.dart';

import '../../../shared/widgets/app_navigation_bar.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  final Widget child;

  const DashboardScreen({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Image.asset(
              'assets/logo/app_logo.png',
              height: 24,
            ),
            const SizedBox(width: 8),
            Image.asset(
              'assets/logo/app_logo_text.png',
              height: 16,
            ),
          ],
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Text(
                  ref.watch(loginProvider).valueOrNull?.displayName ?? 'User',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 8),
                CircleAvatar(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  radius: 16,
                  child: Text(
                    ref
                            .watch(loginProvider)
                            .valueOrNull
                            ?.displayName
                            ?.substring(0, 1)
                            .toUpperCase() ??
                        'U',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.logout),
                  onPressed: () {
                    ref.read(loginProvider.notifier).signOut();
                  },
                  tooltip: 'Logout',
                ),
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          const AppNavigationBar(),
          Expanded(
            child: widget.child,
          ),
        ],
      ),
    );
  }
}
