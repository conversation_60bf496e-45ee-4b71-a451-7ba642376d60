// lib/features/auth/screens/login_screen.dart

import 'package:admin/shared/widgets/app_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:providers/login_provider.dart';

import '../../../shared/utils/error_handler.dart';
import '../../../shared/widgets/app_text_field.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController(text: const String.fromEnvironment('email', defaultValue: ''));
  final _passwordController = TextEditingController(text: const String.fromEnvironment('password', defaultValue: ''));
  bool _showPassword = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _toggleShowPassword() {
    setState(() {
      _showPassword = !_showPassword;
    });
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    await ref.read(loginProvider.notifier).signInWithEmail(
          _emailController.text.trim(),
          _passwordController.text,
        );

    final loginState = ref.read(loginProvider);
    if (loginState.hasError) {
      ErrorHandler.showErrorSnackBar(ref, loginState.error, context: 'Login');
    }
  }

  @override
  Widget build(BuildContext context) {
    final loginState = ref.watch(loginProvider);
    final isLoading = loginState.isLoading;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text('Login'),
      ),
      body: Center(
        child: SingleChildScrollView(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 400),
            padding: const EdgeInsets.all(24),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset(
                        'assets/logo/app_logo.png',
                        width: 43,
                        height: 43,
                      ),
                      const SizedBox(height: 16),
                      Image.asset(
                        'assets/logo/admin_logo.png',
                        height: 24,
                      ),
                      const SizedBox(height: 32),
                      Text(
                        'Sign in',
                        style: theme.textTheme.titleLarge,
                      ),
                      const SizedBox(height: 24),
                      AppTextField(
                        controller: _emailController,
                        label: "Email",
                        hint: "Enter Email ID",
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your email';
                          }
                          if (!value.contains('@')) {
                            return 'Please enter a valid email';
                          }
                          return null;
                        },
                      ),
                      Container(
                        alignment: Alignment.centerRight,
                        child: AppButton(
                          onPressed: () => context.push('/forgot-password'),
                          label: "Forgot Password?",
                          variant: AppButtonVariant.text,
                        ),
                      ),
                      AppTextField(
                        controller: _passwordController,
                        obscureText: !_showPassword,
                        label: "Password",
                        hint: "Enter Password",
                        suffix: IconButton(
                          icon: Icon(
                            _showPassword ? Icons.visibility : Icons.visibility_off,
                            color: theme.hintColor,
                          ),
                          onPressed: _toggleShowPassword,
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your password';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 32),
                      AppButton(
                        label: "Sign in",
                        onPressed: _login,
                        width: double.maxFinite,
                        isLoading: isLoading,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
