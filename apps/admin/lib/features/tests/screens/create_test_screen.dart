// lib/features/tests/screens/create_test_screen.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/test_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:providers/common.dart';
import 'package:providers/test_provider.dart';

import '../../../shared/providers/filter_provider.dart';
import '../../../shared/widgets/app_button.dart';
import '../../../shared/widgets/filter_bar.dart';
import '../../../shared/widgets/status_messages.dart';
import '../providers/test_form_provider.dart';
import '../widgets/question_section.dart';
import '../widgets/test_form_fields.dart';

class CreateTestScreen extends ConsumerStatefulWidget {
  final String? testId;

  const CreateTestScreen({this.testId, super.key});

  @override
  ConsumerState<CreateTestScreen> createState() => _AddTestScreenState();
}

class _AddTestScreenState extends ConsumerState<CreateTestScreen> {
  String? testId;
  bool isLoading = false;
  bool isEditMode = false;

  @override
  void initState() {
    super.initState();

    isEditMode = widget.testId != null;
    testId = widget.testId ?? FirebaseFirestore.instance.collection('tests').doc().id;

    if (isEditMode) {
      _loadTestData();
    }
  }

  Future<void> _loadTestData() async {
    setState(() {
      isLoading = true;
    });

    try {
      final doc = await FirebaseFirestore.instance.collection('tests').doc(testId).get();

      if (doc.exists) {
        final testData = doc.data()!;
        testData['id'] = doc.id;

        final test = TestEntity.fromJson(testData);

        // Initialize filter with test data
        final filterNotifier = ref.read(filterProvider.notifier);
        filterNotifier.setCourse(test.courseId);
        filterNotifier.setYear(test.yearId);
        filterNotifier.setSubject(test.subjectId);
        filterNotifier.setType(test.type);
        filterNotifier.setDifficulty(test.difficulty);

        // Initialize form with test data
        ref.read(testFormProvider.notifier).initWithTest(test);
      }
    } catch (e) {
      dbgPrint('Error loading test: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading test: $e')),
        );
      }
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final title = isEditMode ? 'Edit Test' : 'Add Test';

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        title: Text(title),
        actions: [
          AppButton(
            label: "Cancel",
            onPressed: () => context.pop(),
            variant: AppButtonVariant.outlined,
          ),
          const SizedBox(width: 12),
          AppButton(
            label: "Create Test",
            onPressed: () => saveTest(),
          ),
          const SizedBox(width: 12),
        ],
      ),
      body: isLoading
          ? const LoadingMessage(message: "Loading test data...")
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const FilterBar(),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.only(top: 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const TestFormFields(),
                          const SizedBox(height: 24),
                          const QuestionsSection(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  void saveTest() {
    final filter = ref.read(filterProvider);
    final test = ref.read(testFormProvider);

    // Validate form
    if (filter.courseId == null ||
        filter.yearId == null ||
        filter.subjectId == null ||
        filter.type == null ||
        test.name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please fill all required fields')));
      return;
    }

    if (test.mode == TestMode.paid && test.tier == null) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please select a tier for paid tests')));
      return;
    }

    if (test.questionIds.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please select at least one question')));
      return;
    }

    // Create test entity with the correct ID
    try {
      final testToSave = test.copyWith(
        id: testId,
        courseId: filter.courseId!,
        yearId: filter.yearId!,
        subjectId: filter.subjectId!,
        type: filter.type!,
        difficulty: filter.difficulty!,
      );

      // Save or update the test based on mode
      if (isEditMode) {
        _updateTest(testToSave);
      } else {
        _addTest(testToSave);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
    }
  }

  void _updateTest(TestEntity test) {
    ref.read(testRepositoryProvider.notifier).updateTest(test).then((_) {
      if (!mounted) return;

      context.pop(true);
      ref.invalidate(testFormProvider);
    }).catchError((e) {
      dbgPrint("Error: $e");
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error updating test: ${e.toString()}')));
    });
  }

  void _addTest(TestEntity test) {
    ref.read(testRepositoryProvider.notifier).addTest(test).then((_) {
      if (!mounted) return;
      context.pop(true);
      ref.invalidate(testFormProvider);
    }).catchError((e) {
      dbgPrint("Error: $e");
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error saving test: ${e.toString()}')));
    });
  }
}
