// lib/features/tests/screens/tests_screen.dart

import 'package:admin/shared/utils/string_extensions.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/test_entity.dart';
import 'package:firebase_ui_firestore/firebase_ui_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:providers/common.dart';

import '../../../shared/models/filter_state.dart';
import '../../../shared/providers/filter_provider.dart';
import '../../../shared/widgets/base_content_screen.dart';
import '../../../shared/widgets/content_data_table.dart';
import '../../../shared/widgets/status_messages.dart';
import '../widgets/test_actions.dart';
import '../widgets/test_status_badge.dart';

class TestsScreen extends ConsumerWidget {
  const TestsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filter = ref.watch(filterProvider);

    return BaseContentScreen(
      title: 'Tests',
      addButtonText: 'Add Test',
      onAdd: () => context.push("/add-test"),
      dataTable: _buildContent(context, filter),
      pagination: const SizedBox.shrink(), // Pagination handled by FirestoreQueryBuilder
    );
  }

  Widget _buildContent(BuildContext context, FilterState filter) {
    // Build query based on filters
    final query = _buildTestsQuery(filter);

    // Define columns for the data table
    final columns = [
      const DataColumn(label: Text('Name')),
      const DataColumn(label: Text('Type')),
      const DataColumn(label: Text('Difficulty')),
      const DataColumn(label: Text('Mode')),
      const DataColumn(label: Text('Duration')),
      const DataColumn(label: Text('Questions')),
      const DataColumn(label: Text('Status')),
      const DataColumn(label: Text('Actions')),
    ];

    return FirestoreQueryBuilder<Map<String, dynamic>>(
      query: query,
      pageSize: 20,
      builder: (context, snapshot, _) {
        // Handle loading state
        if (snapshot.isFetching && snapshot.docs.isEmpty) {
          return const LoadingMessage(message: "Loading tests...");
        }

        // Handle error state
        if (snapshot.hasError) {
          dbgPrint("There was a problem retrieving the tests: ${snapshot.error}");

          return ErrorMessage(
            title: "Error Loading Tests",
            message: "There was a problem retrieving the tests: ${snapshot.error}",
            onRetry: () => snapshot.fetchMore(),
          );
        }

        // Handle empty state
        if (snapshot.docs.isEmpty) {
          return const EmptyDataMessage(
            title: "No Tests Found",
            message: "No tests match your current filters. Try changing your filter criteria or add a new test.",
          );
        }

        // Create rows from the fetched documents
        final rows = _buildDataRows(snapshot.docs);

        // Attempt to load more when scrolled near the end
        if (snapshot.hasMore && !snapshot.isFetching) {
          Future.delayed(Duration.zero, () {
            snapshot.fetchMore();
          });
        }

        // Return the data table
        return ContentDataTable(
          columns: columns,
          rows: rows,
          isLoading: false,
        );
      },
    );
  }

  // Create data rows from query documents
  List<DataRow> _buildDataRows(List<QueryDocumentSnapshot<Map<String, dynamic>>> docs) {
    return docs.map((doc) {
      final test = TestEntity.fromJson({
        ...doc.data(),
        'id': doc.id,
      });

      return DataRow(
        cells: [
          // Test Name
          DataCell(
            Tooltip(
              message: test.name,
              child: Text(
                test.name,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),
          // Type
          DataCell(Text(test.type.toString().split('.').last.toCapitalized())),
          // Difficulty
          DataCell(Text(test.difficulty.toString().split('.').last.toCapitalized())),
          // Mode
          DataCell(
            Row(
              children: [
                Text(test.mode.toString().split('.').last.toUpperCase()),
                if (test.mode == TestMode.paid && test.tier != null) Text(', ${test.tier.toString().split('.').last}'),
              ],
            ),
          ),
          // Test Duration
          DataCell(
            Text('${test.duration} min'),
          ),
          // Questions count
          DataCell(
            Text('${test.questionIds.length} questions'),
          ),
          // Status
          DataCell(
            TestStatusBadge(status: test.status),
          ),
          // Actions
          DataCell(
            TestActionMenu(test: test),
          ),
        ],
      );
    }).toList();
  }

  // Helper method to build a filtered query for tests
  Query<Map<String, dynamic>> _buildTestsQuery(FilterState filter) {
    Query<Map<String, dynamic>> query = FirebaseFirestore.instance.collection('tests');

    // Check if any filters are selected
    final bool hasFilters = filter.courseId != null ||
        filter.yearId != null ||
        filter.subjectId != null ||
        filter.type != null ||
        filter.difficulty != null ||
        filter.status != null;

    // If no filters are selected, just return all tests with default ordering
    if (!hasFilters) {
      return query.orderBy('updatedAt', descending: true);
    }

    // Apply filters if provided
    if (filter.courseId != null) {
      query = query.where('courseId', isEqualTo: filter.courseId);
    }
    if (filter.yearId != null) {
      query = query.where('yearId', isEqualTo: filter.yearId);
    }
    if (filter.subjectId != null) {
      query = query.where('subjectId', isEqualTo: filter.subjectId);
    }
    if (filter.type != null) {
      String? typeStr = filter.type?.name.toSnakeCase();
      query = query.where('type', isEqualTo: typeStr);
    }
    if (filter.difficulty != null) {
      String difficultyStr = filter.difficulty.toString().split('.').last.toLowerCase();
      query = query.where('difficulty', isEqualTo: difficultyStr);
    }
    if (filter.status != null) {
      String statusStr = filter.status.toString().split('.').last.toLowerCase();
      query = query.where('status', isEqualTo: statusStr);
    }

    // Order by updated time by default
    return query.orderBy('updatedAt', descending: true);
  }
}
