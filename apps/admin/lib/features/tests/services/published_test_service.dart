import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:entities/published_test_entity.dart';
import 'package:entities/question_entity.dart';
import 'package:entities/test_entity.dart';
import 'package:entities/test_enums.dart';
import 'package:entities/courses_entity.dart';
import 'package:entities/section_entity.dart';
import 'package:entities/topic_entity.dart';
import 'dart:async';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:http/http.dart' as http;
import 'package:firebase_storage/firebase_storage.dart';

import '../../../shared/utils/string_encryption_util.dart';

/// Service class responsible for assembling and managing published tests
class PublishedTestService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _testsCollection = 'tests';
  static const String _questionsCollection = 'questions';
  static const String _publishedTestsCollection = 'published_test';
  static const String _publishedFreeCollection = 'published_free';
  static const String _testsSubCollection = 'tests';
  static const String _versionsSubCollection = 'versions';
  static const String _courseCatalogCollection = 'course_catalog';
  static const String _structureDocument = 'structure';

  /// Fetches a test entity and verifies all its questions without saving
  static Future<PublishedTestEntity> assemblePublishedTest({
    required String testId,
  }) async {
    if (testId.isEmpty) {
      throw ArgumentError('Test ID cannot be empty');
    }

    // 1. Fetch the test
    final testEntity = await _fetchTestEntity(testId);

    // 2. Create the subscription ID (courseId:yearId)
    final subscriptionId = '${testEntity.courseId}:${testEntity.yearId}';

    // 3. Fetch and validate all questions
    final questions = await _fetchAndValidateQuestions(testEntity.questionIds);

    // 4. Process all images to extract dimensions
    final processedQuestions = await _processImagesForAllQuestions(questions);

    // 5. Determine the next version number using subscriptionId and testId
    final nextVersion = await _determineNextVersion(subscriptionId, testId);

    // 6. Create the published test entity with the original testId and processed questions
    final publishedTest = _createPublishedTestEntity(testEntity, nextVersion, processedQuestions, testId);

    // 7. Encrypt the string values in the published test
    final encryptedTest = _encryptPublishedTest(publishedTest);

    return encryptedTest;
  }

  /// Fetches a test entity by ID
  static Future<TestEntity> _fetchTestEntity(String testId) async {
    final testDoc = await _firestore.collection(_testsCollection).doc(testId).get();

    if (!testDoc.exists || testDoc.data() == null) {
      throw Exception('Test with ID $testId not found');
    }

    final testEntity = TestEntity.fromJson(testDoc.data()!);

    if (testEntity.questionIds.isEmpty) {
      throw Exception('Test has no questions');
    }

    return testEntity;
  }

  /// Fetches and validates all questions for a test
  static Future<List<QuestionEntity>> _fetchAndValidateQuestions(List<String> questionIds) async {
    if (questionIds.isEmpty) {
      throw Exception('No question IDs provided');
    }

    // Fetch all questions in parallel
    final questionsSnapshots = await Future.wait(
        questionIds.map((questionId) => _firestore.collection(_questionsCollection).doc(questionId).get()));

    // Verify all questions exist and are published
    final missingQuestionIds = <String>[];
    final unpublishedQuestionIds = <String>[];
    final List<QuestionEntity> questions = [];

    for (var i = 0; i < questionsSnapshots.length; i++) {
      final questionId = questionIds[i];
      final snapshot = questionsSnapshots[i];

      if (!snapshot.exists || snapshot.data() == null) {
        missingQuestionIds.add(questionId);
        continue;
      }

      final questionEntity = QuestionEntity.fromJson(snapshot.data()!);

      // Ensure the question entity has its ID set correctly
      if (questionEntity.id == null || questionEntity.id!.isEmpty) {
        // Create a new instance with the ID from Firestore
        final updatedQuestion = questionEntity.copyWith(id: questionId);
        questions.add(updatedQuestion);
      } else {
        questions.add(questionEntity);
      }

      // Questions no longer have status - all are considered published/available
      // if (questionEntity.status != QuestionStatus.published) {
      //   unpublishedQuestionIds.add(questionId);
      // }
    }

    // Throw exception if any questions are missing or not published
    if (missingQuestionIds.isNotEmpty || unpublishedQuestionIds.isNotEmpty) {
      throw QuestionsVerificationException(
        missingQuestionIds: missingQuestionIds,
        unpublishedQuestionIds: unpublishedQuestionIds,
      );
    }

    return questions;
  }

  /// Process all images in all questions to extract dimensions
  static Future<List<QuestionEntity>> _processImagesForAllQuestions(List<QuestionEntity> questions) async {
    // Process all questions in parallel
    final updatedQuestions = await Future.wait(questions.map((question) => _processImagesForQuestion(question)));

    return updatedQuestions;
  }

  /// Process all images in a single question to extract dimensions
  static Future<QuestionEntity> _processImagesForQuestion(QuestionEntity question) async {
    // Create futures for all image processing operations
    final futures = <Future>[];

    // Temp variables to store results
    double? questionImageWidth;
    double? questionImageHeight;
    double? answerImageWidth;
    double? answerImageHeight;
    final optionImagesWidth = List<double?>.filled(question.optionImages.length, null);
    final optionImagesHeight = List<double?>.filled(question.optionImages.length, null);

    // Process question image
    if (question.questionImage != null && question.questionImage!.isNotEmpty) {
      futures.add(_getImageDimensions(question.questionImage!).then((dimensions) {
        questionImageWidth = dimensions.width;
        questionImageHeight = dimensions.height;
      }));
    }

    // Process answer image
    if (question.answerImage != null && question.answerImage!.isNotEmpty) {
      futures.add(_getImageDimensions(question.answerImage!).then((dimensions) {
        answerImageWidth = dimensions.width;
        answerImageHeight = dimensions.height;
      }));
    }

    // Process option images
    for (int i = 0; i < question.optionImages.length; i++) {
      final imagePath = question.optionImages[i];
      if (imagePath.isNotEmpty) {
        futures.add(_getImageDimensions(imagePath).then((dimensions) {
          optionImagesWidth[i] = dimensions.width;
          optionImagesHeight[i] = dimensions.height;
        }));
      } else {
        optionImagesWidth[i] = 0;
        optionImagesHeight[i] = 0;
      }
    }

    // Wait for all image processing to complete
    await Future.wait(futures);

    // Return updated question with all dimensions
    return question.copyWith(
      questionImageWidth: questionImageWidth,
      questionImageHeight: questionImageHeight,
      answerImageWidth: answerImageWidth,
      answerImageHeight: answerImageHeight,
      optionImagesWidth: optionImagesWidth.whereType<double>().toList(),
      optionImagesHeight: optionImagesHeight.whereType<double>().toList(),
    );
  }

  /// Get image dimensions from a URL or storage path
  static Future<ui.Size> _getImageDimensions(String imagePathOrUrl) async {
    try {
      final isHttpUrl = imagePathOrUrl.startsWith('http://') || imagePathOrUrl.startsWith('https://');
      final imageUrl = isHttpUrl ? imagePathOrUrl : await _getDownloadUrlFromPath(imagePathOrUrl);

      // Download image data
      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode != 200) {
        throw Exception('Failed to download image: $imageUrl with status ${response.statusCode}');
      }

      // Decode image to get dimensions
      final imageSize = await _decodeImageDimensions(response.bodyBytes);
      return imageSize;
    } catch (e) {
      throw Exception('Failed to process image dimensions for: $imagePathOrUrl. Error: $e');
    }
  }

  /// Convert a Firebase Storage path to a download URL
  static Future<String> _getDownloadUrlFromPath(String storagePath) async {
    try {
      final ref = FirebaseStorage.instance.ref(storagePath);
      return await ref.getDownloadURL();
    } catch (e) {
      throw Exception('Failed to get download URL for path: $storagePath. Error: $e');
    }
  }

  /// Decode image bytes to get dimensions
  static Future<ui.Size> _decodeImageDimensions(Uint8List imageBytes) async {
    final completer = Completer<ui.Size>();

    ui.decodeImageFromList(imageBytes, (ui.Image image) {
      completer.complete(ui.Size(
        image.width.toDouble(),
        image.height.toDouble(),
      ));
    });

    return completer.future;
  }

  /// Determines the next version number for a published test
  static Future<int> _determineNextVersion(String subscriptionId, String testId) async {
    // Start with version 1
    int version = 1;

    // Check for existing versions in the subcollection using the new path structure
    final versionsRef = _firestore.collection('$_publishedTestsCollection/$subscriptionId/$_testsSubCollection/$testId/$_versionsSubCollection');

    // Query the subcollection to get all versions
    final versionsSnapshot = await versionsRef.get();

    if (versionsSnapshot.docs.isNotEmpty) {
      // Find the highest version number
      int highestVersion = 0;
      for (var doc in versionsSnapshot.docs) {
        // Try to parse the document ID as an integer
        try {
          final docVersion = int.parse(doc.id);
          if (docVersion > highestVersion) {
            highestVersion = docVersion;
          }
        } catch (e) {
          // Skip documents with non-integer IDs
          continue;
        }
      }

      // Set the next version to be one higher than the highest
      version = highestVersion + 1;
    }

    return version;
  }

  /// Creates a published test entity from a test entity and list of questions
  static PublishedTestEntity _createPublishedTestEntity(
      TestEntity testEntity, int version, List<QuestionEntity> questions, String testId) {
    return PublishedTestEntity(
      id: testId,
      createdAt: testEntity.createdAt,
      tier: testEntity.tier,
      updatedAt: DateTime.now(), // Current time as publish time
      version: version, // Set the version number
      courseId: testEntity.courseId,
      yearId: testEntity.yearId,
      subscriptionId: '${testEntity.courseId}:${testEntity.yearId}',
      subjectId: testEntity.subjectId,
      name: testEntity.name,
      type: testEntity.type,
      duration: testEntity.duration,
      difficulty: testEntity.difficulty,
      status: TestStatus.published,
      mode: testEntity.mode,
      questionCount: questions.length,
      fullQuestionCount: questions.length, // Set fullQuestionCount to the total questions
      questions: questions, // Add the actual question entities
    );
  }

  /// Creates a free version of the published test with only the first five questions
  static PublishedTestEntity _createFreeVersionOfTest(PublishedTestEntity fullTest) {
    if (fullTest.id == null || fullTest.id!.isEmpty) {
      throw ArgumentError('Published test must have a valid ID');
    }

    // Take only the first five questions
    final freeQuestions = fullTest.questions.take(5).toList();

    // Create a new test entity with the free ID
    return fullTest.copyWith(
      id: fullTest.id!, // Keep the same ID but in a different collection
      questions: freeQuestions,
      questionCount: freeQuestions.length, // Set to number of included questions (5)
      fullQuestionCount: fullTest.questions.length, // Preserve the original question count
    );
  }

  /// Encrypts all string values in a published test entity
  static PublishedTestEntity _encryptPublishedTest(PublishedTestEntity publishedTest) {
    if (publishedTest.id == null || publishedTest.id!.isEmpty) {
      throw ArgumentError('Published test must have a valid ID');
    }

    // Generate encryption key from test ID
    final key = StringEncryptionUtil.generateEncryptionKey(publishedTest.id!);

    // Encrypt each question individually
    final encryptedQuestions = publishedTest.questions.map((question) {
      return _encryptQuestionEntity(question, key);
    }).toList();

    // Return a new entity with encrypted questions
    return publishedTest.copyWith(
      // Encrypt test name
      name: StringEncryptionUtil.encryptString(publishedTest.name, key),
      // Replace questions with encrypted versions
      questions: encryptedQuestions,
    );
  }

  /// Encrypts all string values in a question entity
  static QuestionEntity _encryptQuestionEntity(QuestionEntity question, encrypt.Key key) {
    return question.copyWith(
      // Encrypt all string fields
      question: StringEncryptionUtil.encryptString(question.question, key),
      answer: question.answer != null ? StringEncryptionUtil.encryptString(question.answer!, key) : null,
      explanation: question.explanation != null ? StringEncryptionUtil.encryptString(question.explanation!, key) : null,
      reference: question.reference != null ? StringEncryptionUtil.encryptString(question.reference!, key) : null,
      // Encrypt all strings in lists
      options: question.options.map((option) => StringEncryptionUtil.encryptString(option, key)).toList(),
      optionImages: question.optionImages.map((image) => StringEncryptionUtil.encryptString(image, key)).toList(),
      keywords: question.keywords.map((keyword) => StringEncryptionUtil.encryptString(keyword, key)).toList(),
      // Image URLs
      questionImage:
          question.questionImage != null ? StringEncryptionUtil.encryptString(question.questionImage!, key) : null,
      answerImage: question.answerImage != null ? StringEncryptionUtil.encryptString(question.answerImage!, key) : null,
    );
  }

  /// Saves both the current and versioned copies of a published test and updates course entity
  /// Also creates and saves a free version with only the first five questions in a separate collection
  static Future<void> savePublishedTest(PublishedTestEntity publishedTest) async {
    if (publishedTest.id == null || publishedTest.id!.isEmpty) {
      throw ArgumentError('Published test must have a valid ID');
    }

    final String testId = publishedTest.id!;
    final String subscriptionId = publishedTest.subscriptionId;
    final int version = publishedTest.version;

    // Create an unencrypted version to store in the course entity
    // We need to decrypt the name from the encrypted test
    String originalName = publishedTest.name;

    // Try to decrypt the name if we have a valid ID
    try {
      if (publishedTest.id != null && publishedTest.id!.isNotEmpty) {
        final key = StringEncryptionUtil.generateEncryptionKey(publishedTest.id!);
        originalName = StringEncryptionUtil.decryptString(publishedTest.name, key);
      }
    } catch (e) {
      // If decryption fails, continue with the encrypted name
      // This is a fallback in case something goes wrong
    }

    // Create a version with unencrypted name and no questions for the course entity
    final testForCourses = publishedTest.copyWith(
      name: originalName, // Use the original/decrypted name
      questions: [], // Remove questions to save space
    );

    // Create the free version of the test with only the first five questions
    final freeTest = _createFreeVersionOfTest(publishedTest);

    // Use a transaction to ensure atomic operation
    return _firestore.runTransaction((transaction) async {
      // IMPORTANT: All reads must come before any writes in a transaction

      // 1. First, get the courses document to ensure it exists and read its data
      final coursesRef = _firestore.collection(_courseCatalogCollection).doc(_structureDocument);
      final coursesSnapshot = await transaction.get(coursesRef);

      if (!coursesSnapshot.exists || coursesSnapshot.data() == null) {
        throw Exception('Courses document not found in app_config');
      }

      // 2. Prepare the updated courses entity
      final updatedCoursesEntity = _prepareUpdatedCoursesEntity(coursesSnapshot.data()!, testForCourses);

      // 3. Once all reads are complete, perform writes
      // Get references to the main test documents using the new path structure
      // Full test in published_test/{subscriptionId}/tests/{testId}
      final mainDocRef = _firestore.doc('$_publishedTestsCollection/$subscriptionId/$_testsSubCollection/$testId');

      // Free test in published_free/{subscriptionId}/tests/{testId}
      final freeMainDocRef = _firestore.doc('$_publishedFreeCollection/$subscriptionId/$_testsSubCollection/$testId');

      // Get references to the version documents in sub-collections
      final versionedDocRef = _firestore.doc('$_publishedTestsCollection/$subscriptionId/$_testsSubCollection/$testId/$_versionsSubCollection/$version');
      final freeVersionedDocRef = _firestore.doc('$_publishedFreeCollection/$subscriptionId/$_testsSubCollection/$testId/$_versionsSubCollection/$version');

      // Perform writes
      transaction.set(mainDocRef, publishedTest.toJson()); // The already-encrypted full test
      transaction.set(versionedDocRef, publishedTest.toJson()); // The already-encrypted full test (versioned)
      transaction.set(freeMainDocRef, freeTest.toJson()); // The free version with 5 questions in new collection
      transaction.set(freeVersionedDocRef, freeTest.toJson()); // The free version with 5 questions (versioned)
      transaction.set(coursesRef, updatedCoursesEntity.toJson()); // Updated courses with unencrypted test name
    });
  }

  /// Prepares an updated courses entity with the test added to the appropriate subject
  static CoursesEntity _prepareUpdatedCoursesEntity(Map<String, dynamic> coursesData, PublishedTestEntity test) {
    if (test.id == null || test.id!.isEmpty) {
      throw ArgumentError('Test ID cannot be empty');
    }

    try {
      // Parse the courses entity
      final coursesEntity = CoursesEntity.fromJson(coursesData);

      // Bail early if course doesn't exist
      if (!coursesEntity.courses.containsKey(test.courseId)) {
        throw Exception('Course with ID ${test.courseId} not found');
      }

      // Get the course
      final course = coursesEntity.courses[test.courseId]!;

      // Bail early if year doesn't exist
      if (!course.years.containsKey(test.yearId)) {
        throw Exception('Year with ID ${test.yearId} not found in course ${test.courseId}');
      }

      // Get the year
      final year = course.years[test.yearId]!;

      // Bail early if subject doesn't exist
      if (!year.subjects.containsKey(test.subjectId)) {
        throw Exception('Subject with ID ${test.subjectId} not found in year ${test.yearId}');
      }

      // Get the subject
      final subject = year.subjects[test.subjectId]!;

      // For published tests, use a default section and topic structure
      const defaultSectionName = 'Published Tests';
      const defaultTopicName = 'General';

      // Get or create the default section
      final currentSections = Map<String, SectionEntity>.from(subject.sections);
      final defaultSection = currentSections[defaultSectionName] ?? SectionEntity();

      // Get or create the default topic within the section
      final currentTopics = Map<String, TopicEntity>.from(defaultSection.topics);
      final defaultTopic = currentTopics[defaultTopicName] ?? TopicEntity();

      // Add the test to the default topic
      // Use the full format courseId:yearId:testId as the key for proper path resolution
      final fullTestId = '${test.courseId}:${test.yearId}:${test.id!}';
      final updatedTopic = defaultTopic.copyWith(
        tests: {
          ...defaultTopic.tests,
          fullTestId: test, // Using test with unencrypted name and no questions
        },
      );

      // Update the topics map
      currentTopics[defaultTopicName] = updatedTopic;

      // Update the section with the new topics
      final updatedSection = defaultSection.copyWith(topics: currentTopics);

      // Update the sections map
      currentSections[defaultSectionName] = updatedSection;

      // Create an updated subject with the new section structure
      final updatedSubject = subject.copyWith(sections: currentSections);

      // Create updated subjects map
      final updatedSubjects = {
        ...year.subjects,
        test.subjectId: updatedSubject,
      };

      // Create updated year
      final updatedYear = year.copyWith(
        subjects: updatedSubjects,
      );

      // Create updated years map
      final updatedYears = {
        ...course.years,
        test.yearId: updatedYear,
      };

      // Create updated course
      final updatedCourse = course.copyWith(
        years: updatedYears,
      );

      // Create updated courses map
      final updatedCourses = {
        ...coursesEntity.courses,
        test.courseId: updatedCourse,
      };

      // Create and return updated courses entity
      return coursesEntity.copyWith(
        courses: updatedCourses,
      );
    } catch (e) {
      throw Exception('Failed to update course with test: ${e.toString()}');
    }
  }
}

/// Exception thrown when one or more questions are missing or not published
class QuestionsVerificationException implements Exception {
  final List<String> missingQuestionIds;
  final List<String> unpublishedQuestionIds;

  QuestionsVerificationException({
    this.missingQuestionIds = const [],
    this.unpublishedQuestionIds = const [],
  });

  @override
  String toString() {
    final buffer = StringBuffer('QuestionsVerificationException: ');

    if (missingQuestionIds.isNotEmpty) {
      buffer.write('Missing questions: ${missingQuestionIds.join(', ')}. ');
    }

    if (unpublishedQuestionIds.isNotEmpty) {
      buffer.write('Unpublished questions: ${unpublishedQuestionIds.join(', ')}.');
    }

    return buffer.toString();
  }
}
