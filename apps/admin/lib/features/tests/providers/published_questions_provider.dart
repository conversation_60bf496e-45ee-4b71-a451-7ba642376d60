// lib/features/tests/providers/published_questions_provider.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/question_entity.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'published_questions_provider.g.dart';

@riverpod
Future<List<QuestionEntity>> publishedQuestions(
  Ref ref, {
  required String courseId,
  required String yearId,
  required String subjectId,
}) async {
  Query<Map<String, dynamic>> query =
      FirebaseFirestore.instance.collection('questions');

  // Apply filters
  query = query.where('courseId', isEqualTo: courseId);
  query = query.where('yearId', isEqualTo: yearId);
  query = query.where('subjectId', isEqualTo: subjectId);
  query = query.where('status', isEqualTo: QuestionStatus.published.name);

  final querySnapshot = await query.get();

  return querySnapshot.docs
      .map((doc) => QuestionEntity.fromJson({
            ...doc.data(),
            'id': doc.id,
          }))
      .toList();
}
