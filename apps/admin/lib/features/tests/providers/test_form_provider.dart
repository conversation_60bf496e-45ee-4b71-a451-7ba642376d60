// lib/features/tests/providers/test_form_provider.dart

import 'package:entities/question_entity.dart';
import 'package:entities/test_entity.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../shared/providers/filter_provider.dart';

part 'test_form_provider.g.dart';

@riverpod
class TestForm extends _$TestForm {
  @override
  TestEntity build() {
    final filter = ref.watch(filterProvider);

    // Create default test entity with values from filter when available
    return TestEntity(
      courseId: filter.courseId ?? '',
      yearId: filter.yearId ?? '',
      subjectId: filter.subjectId ?? '',
      name: '',
      type: filter.type ?? QuestionType.mcq,
      difficulty: filter.difficulty ?? QuestionDifficulty.easy,
      status: QuestionStatus.draft,
      mode: TestMode.free,
      tier: null,
      questionIds: [],
      duration: 60,
    );
  }

  void initWithTest(TestEntity test) {
    state = test;
  }

  void setName(String name) {
    state = state.copyWith(name: name);
  }

  void setMode(TestMode mode) {
    // Clear tier if switching to free mode
    if (mode == TestMode.free) {
      state = state.copyWith(mode: mode, tier: null);
    } else {
      state = state.copyWith(mode: mode);
    }
  }

  void setTier(TestTier? tier) {
    state = state.copyWith(tier: tier);
  }

  void setDuration(int duration) {
    state = state.copyWith(duration: duration);
  }

  void addQuestion(String questionId) {
    if (!state.questionIds.contains(questionId)) {
      final questionIds = List<String>.from(state.questionIds)..add(questionId);
      state = state.copyWith(questionIds: questionIds);
    }
  }

  void removeQuestion(String questionId) {
    final questionIds = List<String>.from(state.questionIds)..removeWhere((id) => id == questionId);
    state = state.copyWith(questionIds: questionIds);
  }

  void setQuestions(List<String> questionIds) {
    state = state.copyWith(questionIds: questionIds);
  }

  void reset() {
    final filter = ref.read(filterProvider);

    state = TestEntity(
      courseId: filter.courseId ?? '',
      yearId: filter.yearId ?? '',
      subjectId: filter.subjectId ?? '',
      name: '',
      type: filter.type ?? QuestionType.mcq,
      difficulty: filter.difficulty ?? QuestionDifficulty.easy,
      status: QuestionStatus.draft,
      mode: TestMode.free,
      tier: null,
      questionIds: [],
      duration: 60,
    );
  }
}
