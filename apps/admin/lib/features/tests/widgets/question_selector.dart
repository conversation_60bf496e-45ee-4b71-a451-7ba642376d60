// lib/features/tests/widgets/question_selector.dart

import 'package:cached_network_image/cached_network_image.dart';
import 'package:entities/question_entity.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/test_form_provider.dart';

// Create a separate stateful widget for the individual question item
class QuestionItem extends ConsumerStatefulWidget {
  final QuestionEntity question;
  final bool isSelected;
  final Function(String, bool) onToggle;

  const QuestionItem({
    required this.question,
    required this.isSelected,
    required this.onToggle,
    Key? key,
  }) : super(key: key);

  @override
  ConsumerState<QuestionItem> createState() => _QuestionItemState();
}

class _QuestionItemState extends ConsumerState<QuestionItem> {
  String? _imageUrl;
  bool _isLoadingImage = false;

  @override
  void initState() {
    super.initState();
    _loadImageUrl();
  }

  Future<void> _loadImageUrl() async {
    if (widget.question.questionImage != null && widget.question.questionImage!.isNotEmpty) {
      setState(() {
        _isLoadingImage = true;
      });

      try {
        final url = await FirebaseStorage.instance.ref(widget.question.questionImage!).getDownloadURL();

        if (mounted) {
          setState(() {
            _imageUrl = url;
            _isLoadingImage = false;
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoadingImage = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return CheckboxListTile(
      title: Text(
        widget.question.question,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Text(
        'Difficulty: ${widget.question.difficulty.name}',
        style: const TextStyle(fontSize: 12),
      ),
      value: widget.isSelected,
      onChanged: (bool? value) {
        if (value != null) {
          widget.onToggle(widget.question.id!, value);
        }
      },
      secondary: widget.question.questionImage != null ? _buildQuestionImage() : null,
    );
  }

  Widget _buildQuestionImage() {
    if (_isLoadingImage) {
      return Container(
        width: 40,
        height: 40,
        color: Colors.grey[200],
        child: const Center(
          child: SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ),
      );
    }

    if (_imageUrl == null) {
      return Container(
        width: 40,
        height: 40,
        color: Colors.grey[300],
        child: const Icon(Icons.error_outline, size: 20),
      );
    }

    return SizedBox(
      width: 40,
      height: 40,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: CachedNetworkImage(
          imageUrl: _imageUrl!,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: Colors.grey[200],
            child: const Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
          ),
          errorWidget: (context, url, error) => Container(
            color: Colors.grey[300],
            child: const Icon(Icons.broken_image, size: 20),
          ),
        ),
      ),
    );
  }
}

class QuestionSelector extends ConsumerWidget {
  final List<QuestionEntity> questions;
  final List<String> selectedIds;

  const QuestionSelector({
    Key? key,
    required this.questions,
    required this.selectedIds,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Selected: ${selectedIds.length} of ${questions.length}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: () {
                if (selectedIds.length == questions.length) {
                  // Deselect all
                  ref.read(testFormProvider.notifier).setQuestions([]);
                } else {
                  // Select all
                  ref.read(testFormProvider.notifier).setQuestions(questions.map((q) => q.id!).toList());
                }
              },
              child: Text(
                selectedIds.length == questions.length ? 'Deselect All' : 'Select All',
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ListView.builder(
              itemCount: questions.length,
              itemBuilder: (context, index) {
                final question = questions[index];
                final isSelected = selectedIds.contains(question.id);

                return QuestionItem(
                  key: ValueKey(question.id),
                  question: question,
                  isSelected: isSelected,
                  onToggle: (questionId, selected) {
                    if (selected) {
                      ref.read(testFormProvider.notifier).addQuestion(questionId);
                    } else {
                      ref.read(testFormProvider.notifier).removeQuestion(questionId);
                    }
                  },
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
