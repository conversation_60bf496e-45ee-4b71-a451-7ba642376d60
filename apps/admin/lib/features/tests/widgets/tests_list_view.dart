// lib/features/tests/widgets/tests_list_view.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/test_entity.dart';
import 'package:firebase_ui_firestore/firebase_ui_firestore.dart';
import 'package:flutter/material.dart';

import '../../../shared/utils/string_extensions.dart';
import '../../../shared/widgets/status_messages.dart';
import '../widgets/test_actions.dart';
import '../widgets/test_status_badge.dart';

class TestsListView extends StatelessWidget {
  final Query<Map<String, dynamic>> query;
  final int pageSize;
  final Widget? emptyWidget;

  const TestsListView({
    super.key,
    required this.query,
    this.pageSize = 20,
    this.emptyWidget,
  });

  @override
  Widget build(BuildContext context) {
    return FirestoreListView<Map<String, dynamic>>(
      query: query,
      pageSize: pageSize,
      loadingBuilder: (context) => const LoadingMessage(message: "Loading tests..."),
      errorBuilder: (context, error, stackTrace) => ErrorMessage(
        title: "Error Loading Tests",
        message: "There was a problem retrieving the tests: ${error.toString()}",
        onRetry: () {}, // FirestoreListView handles retries internally
      ),
      emptyBuilder: (context) =>
          emptyWidget ??
          const EmptyDataMessage(
            title: "No Tests Found",
            message: "No tests match your current filters. Try changing your filter criteria or add a new test.",
          ),
      itemBuilder: (context, snapshot) {
        final test = TestEntity.fromJson({
          ...snapshot.data(),
          'id': snapshot.id,
        });
        return _buildTestRow(context, test);
      },
    );
  }

  Widget _buildTestRow(BuildContext context, TestEntity test) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            // Test Name
            Expanded(
              flex: 3,
              child: Tooltip(
                message: test.name,
                child: Text(
                  test.name,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
            ),

            // Type
            SizedBox(
              width: 80,
              child: Text(test.type.name.toCapitalized()),
            ),

            // Difficulty
            SizedBox(
              width: 80,
              child: Text(test.difficulty.name.toCapitalized()),
            ),

            // Mode
            SizedBox(
              width: 120,
              child: Row(
                children: [
                  Text(test.mode.name.toUpperCase()),
                  if (test.mode == TestMode.paid && test.tier != null)
                    Text(', ${test.tier.toString().split('.').last}'),
                ],
              ),
            ),

            // Test Duration
            SizedBox(
              width: 80,
              child: Text('${test.duration} min'),
            ),

            // Questions count
            SizedBox(
              width: 100,
              child: Text('${test.questionIds.length} questions'),
            ),

            // Status
            SizedBox(
              width: 100,
              child: TestStatusBadge(status: test.status),
            ),

            // Actions
            SizedBox(
              width: 60,
              child: TestActionMenu(test: test),
            ),
          ],
        ),
      ),
    );
  }
}
