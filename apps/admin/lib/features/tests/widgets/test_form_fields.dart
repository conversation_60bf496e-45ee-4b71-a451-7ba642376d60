// lib/features/tests/widgets/test_form_fields.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../shared/widgets/app_form_field.dart';
import '../providers/test_form_provider.dart';

class TestFormFields extends ConsumerWidget {
  const TestFormFields({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final test = ref.watch(testFormProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Test Name
        AppFormField(
          label: 'Test Name',
          isRequired: true,
          child: TextFormField(
            decoration: const InputDecoration(
              hintText: 'Enter test name',
            ),
            initialValue: test.name,
            onChanged: (value) {
              ref.read(testFormProvider.notifier).setName(value);
            },
          ),
        ),

        const SizedBox(height: 16),

        // Duration field
        AppFormField(
          label: 'Duration (minutes)',
          isRequired: true,
          child: TextFormField(
            decoration: const InputDecoration(
              hintText: 'Enter test duration in minutes',
            ),
            initialValue: test.duration.toString(),
            keyboardType: TextInputType.number,
            onChanged: (value) {
              final duration = int.tryParse(value);
              if (duration != null) {
                ref.read(testFormProvider.notifier).setDuration(duration);
              }
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a duration';
              }
              final duration = int.tryParse(value);
              if (duration == null || duration <= 0) {
                return 'Please enter a valid duration';
              }
              return null;
            },
          ),
        ),

        const SizedBox(height: 16),

        // Test Mode
        // AppFormField(
        //   label: 'Mode',
        //   isRequired: true,
        //   child: DropdownButtonFormField<TestMode>(
        //     decoration: const InputDecoration(
        //       hintText: 'Select mode',
        //     ),
        //     value: test.mode,
        //     items: TestMode.values
        //         .map((mode) => DropdownMenuItem(
        //               value: mode,
        //               child: Text(mode.name.toUpperCase()),
        //             ))
        //         .toList(),
        //     onChanged: (value) {
        //       if (value != null) {
        //         ref.read(testFormProvider.notifier).setMode(value);
        //       }
        //     },
        //   ),
        // ),

        // Tier (only for paid tests)
        // if (test.mode == TestMode.paid)
        //   AppFormField(
        //     label: 'Tier',
        //     isRequired: true,
        //     child: DropdownButtonFormField<TestTier>(
        //       decoration: const InputDecoration(
        //         hintText: 'Select tier',
        //       ),
        //       value: test.tier,
        //       items: TestTier.values
        //           .map((tier) => DropdownMenuItem(
        //                 value: tier,
        //                 child: Text(tier.toString().split('.').last),
        //               ))
        //           .toList(),
        //       onChanged: (value) {
        //         ref.read(testFormProvider.notifier).setTier(value);
        //       },
        //     ),
        //   ),
      ],
    );
  }
}
