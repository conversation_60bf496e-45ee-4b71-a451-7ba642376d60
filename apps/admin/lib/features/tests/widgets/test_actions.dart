// lib/features/tests/widgets/test_actions.dart

import 'package:entities/question_entity.dart';
import 'package:entities/test_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:providers/common.dart';
import 'package:providers/test_provider.dart';

import '../../../shared/utils/error_handler.dart';
import '../services/published_test_service.dart';

class TestActionMenu extends ConsumerWidget {
  final TestEntity test;

  const TestActionMenu({
    super.key,
    required this.test,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert),
      onSelected: (value) {
        if (value == 'edit') {
          _editTest(context, test);
        } else if (value == 'delete') {
          _deleteTest(context, ref, test);
        } else if (value == 'publish') {
          _publishTest(context, ref, test);
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem<String>(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 20),
              SizedBox(width: 8),
              Text('Edit'),
            ],
          ),
        ),
        if (test.status != QuestionStatus.published)
          const PopupMenuItem<String>(
            value: 'publish',
            child: Row(
              children: [
                Icon(Icons.publish, size: 20),
                SizedBox(width: 8),
                Text('Publish'),
              ],
            ),
          ),
        const PopupMenuItem<String>(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 20),
              SizedBox(width: 8),
              Text('Delete'),
            ],
          ),
        ),
      ],
    );
  }

  void _editTest(BuildContext context, TestEntity test) {
    context.push('/edit-test/${test.id}');
  }

  void _deleteTest(BuildContext context, WidgetRef ref, TestEntity test) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete Test'),
        content: const Text('Are you sure you want to delete this test? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(dialogContext);

              if (test.id != null) {
                try {
                  await ref.read(testRepositoryProvider.notifier).deleteTest(test.id!);

                  if (context.mounted) {
                    ErrorHandler.showSuccessSnackBar(ref, 'Test deleted successfully');
                  }
                } catch (error) {
                  if (context.mounted) {
                    ErrorHandler.showErrorSnackBar(ref, error, context: 'Delete Test');
                  }
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _publishTest(BuildContext context, WidgetRef ref, TestEntity test) {
    if (test.id == null) return;
    dbgPrint("Publish Test: ${test.id}");

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Publish Test'),
        content: const Text('Are you sure you want to publish this test? It will be available to students.'),
        actions: [
          TextButton(
            onPressed: () => context.pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              context.pop();

              if (context.mounted) {
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (innerContext) {
                    return const Dialog(
                      child: Padding(
                        padding: EdgeInsets.all(20.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text("Publishing test..."),
                          ],
                        ),
                      ),
                    );
                  },
                );
              }

              try {
                final publishedTest = await PublishedTestService.assemblePublishedTest(
                  testId: test.id!,
                );

                await PublishedTestService.savePublishedTest(publishedTest);

                await ref.read(testRepositoryProvider.notifier).publishTest(test.id!);

                if (context.mounted) {
                  context.pop();
                  ErrorHandler.showSuccessSnackBar(ref, 'Test published successfully');
                }
              } catch (e) {
                dbgPrint("Error in Publish Test: $e");

                if (e is QuestionsVerificationException) {
                  if (context.mounted) {
                    context.pop();
                    _showVerificationErrorDialog(context, e);
                  }
                } else {
                  if (context.mounted) {
                    context.pop();
                    ErrorHandler.showErrorSnackBar(ref, e, context: 'Publish Test');
                  }
                }
              }
            },
            child: const Text('Publish'),
          ),
        ],
      ),
    );
  }

  void _showVerificationErrorDialog(BuildContext context, QuestionsVerificationException error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Publishing Failed'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (error.missingQuestionIds.isNotEmpty) ...[
                const Text('Missing questions:', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                ...error.missingQuestionIds.map((id) => Text('• $id')),
                const SizedBox(height: 16),
              ],
              if (error.unpublishedQuestionIds.isNotEmpty) ...[
                const Text('Unpublished questions:', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                ...error.unpublishedQuestionIds.map((id) => Text('• $id')),
              ],
              const SizedBox(height: 16),
              const Text(
                'All questions must exist and be published before you can publish this test.',
                style: TextStyle(fontStyle: FontStyle.italic),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
