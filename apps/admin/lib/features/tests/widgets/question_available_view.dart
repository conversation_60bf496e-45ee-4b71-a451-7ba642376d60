// lib/features/tests/widgets/question_available_view.dart

import 'package:entities/question_entity.dart';
import 'package:flutter/material.dart';

import '../widgets/question_selector.dart';

class QuestionsAvailableView extends StatelessWidget {
  final List<QuestionEntity> questions;
  final List<String> selectedIds;

  const QuestionsAvailableView({
    required this.questions,
    required this.selectedIds,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available questions: ${questions.length}',
          style: const TextStyle(
            fontStyle: FontStyle.italic,
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: QuestionSelector(
            questions: questions,
            selectedIds: selectedIds,
          ),
        ),
      ],
    );
  }
}
