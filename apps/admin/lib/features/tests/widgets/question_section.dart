// lib/features/tests/widgets/question_section.dart

import 'package:admin/features/tests/widgets/question_available_view.dart';
import 'package:admin/shared/utils/string_extensions.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/question_entity.dart';
import 'package:entities/test_entity.dart';
import 'package:firebase_ui_firestore/firebase_ui_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../shared/models/filter_state.dart';
import '../../../shared/providers/filter_provider.dart';
import '../../../shared/widgets/status_messages.dart';
import '../providers/test_form_provider.dart';

class QuestionsSection extends ConsumerWidget {
  const QuestionsSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final test = ref.watch(testFormProvider);
    final filter = ref.watch(filterProvider);

    // Check if all required filters are selected
    final areRequiredFiltersSelected = filter.courseId != null && filter.yearId != null && filter.subjectId != null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select Questions',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        _buildQuestionContent(context, areRequiredFiltersSelected, filter, test),
      ],
    );
  }

  Widget _buildQuestionContent(
      BuildContext context, bool areRequiredFiltersSelected, FilterState filter, TestEntity test) {
    if (!areRequiredFiltersSelected) {
      return const FilterSelectionMessage();
    }

    // Build query for published questions
    final query = _buildPublishedQuestionsQuery(filter);

    return SizedBox(
      height: 500, // Set appropriate height
      child: FirestoreQueryBuilder<Map<String, dynamic>>(
        query: query,
        pageSize: 20,
        builder: (context, snapshot, _) {
          // Handle loading state
          if (snapshot.isFetching && snapshot.docs.isEmpty) {
            return const LoadingMessage(message: "Loading questions...");
          }

          // Handle error state
          if (snapshot.hasError) {
            return ErrorMessage(
              title: "Error Loading Questions",
              message: "There was a problem retrieving the available questions: ${snapshot.error}",
              onRetry: () => snapshot.fetchMore(),
            );
          }

          // Handle empty state
          if (snapshot.docs.isEmpty) {
            return const EmptyDataMessage(
              title: "No Published Questions Available",
              message:
                  "No published questions match your current filters. Try changing your filter criteria or publish some questions first.",
            );
          }

          // Convert documents to Question entities
          final questions = snapshot.docs
              .map((doc) => QuestionEntity.fromJson({
                    ...doc.data(),
                    'id': doc.id,
                  }))
              .toList();

          // Fetch more when needed
          if (snapshot.hasMore) {
            Future.delayed(Duration.zero, () {
              snapshot.fetchMore();
            });
          }

          // Display the questions available view
          return QuestionsAvailableView(
            questions: questions,
            selectedIds: test.questionIds,
          );
        },
      ),
    );
  }

  // Helper method to build a query for published questions
  Query<Map<String, dynamic>> _buildPublishedQuestionsQuery(FilterState filter) {
    Query<Map<String, dynamic>> query = FirebaseFirestore.instance.collection('questions');

    // Apply required filters
    if (filter.courseId != null) {
      query = query.where('courseId', isEqualTo: filter.courseId);
    }
    if (filter.yearId != null) {
      query = query.where('yearId', isEqualTo: filter.yearId);
    }
    if (filter.subjectId != null) {
      query = query.where('subjectId', isEqualTo: filter.subjectId);
    }

    // Always filter for published questions only
    query = query.where('status', isEqualTo: 'published');

    // Apply type filter if provided
    if (filter.type != null) {
      String? typeStr = filter.type?.name.toSnakeCase();
      query = query.where('type', isEqualTo: typeStr);
    }

    // Apply difficulty filter if provided
    if (filter.difficulty != null) {
      String difficultyStr = filter.difficulty.toString().split('.').last.toLowerCase();
      query = query.where('difficulty', isEqualTo: difficultyStr);
    }

    // Order by updated time
    return query.orderBy('updatedAt', descending: true);
  }
}
