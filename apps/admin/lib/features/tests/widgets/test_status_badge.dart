// lib/features/tests/widgets/test_status_badge.dart

import 'package:entities/question_entity.dart';
import 'package:flutter/material.dart';

class TestStatusBadge extends StatelessWidget {
  final QuestionStatus status;

  const TestStatusBadge({super.key, required this.status});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(_getStatusText()),
    );
  }

  Color _getStatusColor() {
    switch (status) {
      case QuestionStatus.draft:
        return Colors.grey[200]!;
      case QuestionStatus.review:
        return Colors.orange[100]!;
      case QuestionStatus.published:
        return Colors.green[100]!;
      default:
        return Colors.grey[200]!;
    }
  }

  String _getStatusText() {
    switch (status) {
      case QuestionStatus.draft:
        return 'Draft';
      case QuestionStatus.review:
        return 'In Review';
      case QuestionStatus.published:
        return 'Published';
    }
  }
}
