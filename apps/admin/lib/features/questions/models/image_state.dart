// lib/features/questions/models/image_state.dart

import 'package:freezed_annotation/freezed_annotation.dart';

part 'image_state.freezed.dart';
part 'image_state.g.dart';

/// State representing an image in the question form
@freezed
class ImageState with _$ImageState {
  const factory ImageState({
    // The Firebase Storage path
    String? path,

    // Whether the image is currently being uploaded
    @Default(false) bool isUploading,

    // The download URL for display (cached)
    String? downloadUrl,

    // Whether the image has been modified
    @Default(false) bool isDirty,

    // Whether to delete the image when saving
    @Default(false) bool shouldDelete,
  }) = _ImageState;

  factory ImageState.fromJson(Map<String, dynamic> json) => _$ImageStateFromJson(json);

  factory ImageState.initial() => const ImageState();

  /// Create a state for an existing image path
  factory ImageState.withPath(String path, {String? downloadUrl}) => ImageState(
        path: path,
        downloadUrl: downloadUrl,
        isDirty: false,
      );

  /// Create a state for an image being uploaded
  factory ImageState.uploading() => const ImageState(
        isUploading: true,
        isDirty: true,
      );

  /// Create a state for a successfully uploaded image
  factory ImageState.uploaded(String path, {String? downloadUrl}) => ImageState(
        path: path,
        downloadUrl: downloadUrl,
        isUploading: false,
        isDirty: true,
      );

  /// Create a state for an image to be deleted
  factory ImageState.toDelete(String? path) => ImageState(
        path: path,
        shouldDelete: true,
        isDirty: true,
      );
}
