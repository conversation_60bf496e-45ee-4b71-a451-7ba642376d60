// lib/features/questions/screens/questions_screen.dart

import 'package:admin/shared/utils/string_extensions.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/question_entity.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_ui_firestore/firebase_ui_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:providers/common.dart';

import '../../../shared/models/filter_state.dart';
import '../../../shared/providers/filter_provider.dart';
import '../../../shared/widgets/base_content_screen.dart';
import '../../../shared/widgets/content_data_table.dart';
import '../../../shared/widgets/status_messages.dart';
import '../widgets/question_actions.dart';
import '../widgets/question_status_badge.dart';

class QuestionsScreen extends ConsumerWidget {
  const QuestionsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filter = ref.watch(filterProvider);

    return BaseContentScreen(
      title: 'Questions',
      addButtonText: 'Add Question',
      onAdd: () => context.push("/add-questions"),
      dataTable: _QuestionsContent(filter: filter),
      pagination: const SizedBox.shrink(), // Pagination handled by FirestoreListView
    );
  }
}

class _QuestionsContent extends ConsumerWidget {
  final FilterState filter;

  const _QuestionsContent({required this.filter});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Build query based on applied filters
    Query<Map<String, dynamic>> query = _buildFilteredQuery(filter);

    // Define columns for the data table
    final columns = [
      const DataColumn(label: Text('Image')),
      const DataColumn(label: Text('Question')),
      const DataColumn(label: Text('Answer')),
      const DataColumn(label: Text('Type')),
      const DataColumn(label: Text('Difficulty')),
      const DataColumn(label: Text('Status')),
      const DataColumn(label: Text('Actions')),
    ];

    return FirestoreQueryBuilder<Map<String, dynamic>>(
      query: query,
      pageSize: 20,
      builder: (context, snapshot, _) {
        // Handle loading state
        if (snapshot.isFetching && snapshot.docs.isEmpty) {
          return const LoadingMessage(message: "Loading questions...");
        }

        // Handle error state
        if (snapshot.hasError) {
          dbgPrint("There was a problem retrieving the questions: ${snapshot.error}");
          return ErrorMessage(
            title: "Error Loading Questions",
            message: "There was a problem retrieving the questions.",
            onRetry: () => snapshot.fetchMore(),
          );
        }

        // Handle empty state
        if (snapshot.docs.isEmpty) {
          return const EmptyDataMessage(
            title: "No Questions Found",
            message:
                "No questions match your current filters. Try changing your filter criteria or add a new question.",
          );
        }

        // Fetch more data when near the end of the list
        if (snapshot.hasMore && !snapshot.isFetching) {
          Future.delayed(Duration.zero, () {
            snapshot.fetchMore();
          });
        }

        // Create rows from the fetched documents
        final rows = _buildDataRows(snapshot.docs);

        // Return the data table
        return ContentDataTable(
          columns: columns,
          rows: rows,
          isLoading: false,
        );
      },
    );
  }

  // Helper method to build the filtered query
  Query<Map<String, dynamic>> _buildFilteredQuery(FilterState filter) {
    Query<Map<String, dynamic>> query = FirebaseFirestore.instance.collection('questions');

    // Apply filters if provided
    if (filter.courseId != null) {
      query = query.where('courseId', isEqualTo: filter.courseId);
    }
    if (filter.yearId != null) {
      query = query.where('yearId', isEqualTo: filter.yearId);
    }
    if (filter.subjectId != null) {
      query = query.where('subjectId', isEqualTo: filter.subjectId);
    }
    if (filter.type != null) {
      String? typeStr = filter.type?.name.toSnakeCase();
      query = query.where('type', isEqualTo: typeStr);
    }
    if (filter.difficulty != null) {
      String difficultyStr = filter.difficulty.toString().split('.').last.toLowerCase();
      query = query.where('difficulty', isEqualTo: difficultyStr);
    }
    if (filter.status != null) {
      String statusStr = filter.status.toString().split('.').last.toLowerCase();
      query = query.where('status', isEqualTo: statusStr);
    }

    // Order by updated time by default
    return query.orderBy('updatedAt', descending: true);
  }

  // Create data rows from query documents
  List<DataRow> _buildDataRows(List<QueryDocumentSnapshot<Map<String, dynamic>>> docs) {
    return docs.map((doc) {
      final question = QuestionEntity.fromJson({
        ...doc.data(),
        'id': doc.id,
      });

      return DataRow(
        cells: [
          // Image Column
          DataCell(_QuestionImageCell(imagePath: question.questionImage)),
          // Question
          DataCell(
            Tooltip(
              message: question.question,
              child: Text(
                question.question,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ),
          ),
          // Answer
          DataCell(
            Text(
              _getAnswerText(question),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          // Type
          DataCell(
            Text(question.type.toString().split('.').last.toCapitalized()),
          ),
          // Difficulty
          DataCell(
            Text(question.difficulty.toString().split('.').last.toCapitalized()),
          ),
          // Status
          DataCell(
            QuestionStatusBadge(status: question.status),
          ),
          // Actions
          DataCell(
            QuestionActionMenu(question: question),
          ),
        ],
      );
    }).toList();
  }

  // Helper method to get answer text safely
  String _getAnswerText(QuestionEntity question) {
    if (question.type == QuestionType.mcq) {
      // Handle MCQ questions
      if (question.options.isEmpty) {
        return 'No options';
      }

      if (question.correctOptionIndex < 0 || question.correctOptionIndex >= question.options.length) {
        return 'Invalid option';
      }

      return question.options[question.correctOptionIndex];
    } else {
      // Handle FlipCard questions
      return question.answer ?? 'N/A';
    }
  }
}

// Separate widget for question image cell
class _QuestionImageCell extends StatelessWidget {
  final String? imagePath;

  const _QuestionImageCell({this.imagePath});

  @override
  Widget build(BuildContext context) {
    if (imagePath == null || imagePath!.isEmpty) {
      return Container(
        width: 40,
        height: 40,
        color: Colors.grey[300],
        child: const Icon(Icons.image, size: 20),
      );
    }

    return SizedBox(
      width: 50,
      height: 50,
      child: FutureBuilder<String>(
        future: FirebaseStorage.instance.ref(imagePath).getDownloadURL(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Container(
              width: 40,
              height: 40,
              color: Colors.grey[200],
              child: const Center(
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                ),
              ),
            );
          }

          if (snapshot.hasError || !snapshot.hasData) {
            return Container(
              width: 40,
              height: 40,
              color: Colors.grey[300],
              child: const Icon(Icons.error_outline, size: 20),
            );
          }

          return ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: Image.network(
              snapshot.data!,
              fit: BoxFit.cover,
              width: 40,
              height: 40,
              errorBuilder: (context, error, stackTrace) => Container(
                width: 40,
                height: 40,
                color: Colors.grey[300],
                child: const Icon(Icons.error_outline, size: 20),
              ),
            ),
          );
        },
      ),
    );
  }
}
