// lib/features/questions/screens/create_question_screen.dart

import 'package:admin/shared/utils/string_extensions.dart';
import 'package:admin/shared/widgets/app_button.dart';
import 'package:entities/question_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../shared/providers/filter_helpers_provider.dart';
import '../../../shared/providers/filter_provider.dart';
import '../../../shared/utils/error_handler.dart';
import '../../../shared/widgets/app_form_field.dart';
import '../../../shared/widgets/filter_bar.dart';
import '../models/image_state.dart';
import '../providers/question_form_provider.dart';
import '../providers/question_image_provider.dart';
import '../repositories/question_repository.dart';
import '../services/question_service.dart';
import '../widgets/flipcard_section.dart';
import '../widgets/image_selector.dart';
import '../widgets/mcq_section.dart';

/// Custom filter bar that syncs with form state instead of resetting it
class QuestionFilterBar extends ConsumerWidget {
  const QuestionFilterBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filter = ref.watch(filterProvider);
    final question = ref.watch(questionFormProvider);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // Course dropdown
        Expanded(
          child: FilterField(
            label: 'Course *',
            child: AsyncDropdown(
              asyncValue: ref.watch(availableCoursesProvider),
              hint: 'Select Course',
              value: filter.courseId,
              onChanged: (val) {
                ref.read(filterProvider.notifier).setCourse(val);
                if (val != null) {
                  ref.read(questionFormProvider.notifier).setCourse(val);
                }
              },
              itemBuilder: (entry) => DropdownMenuItem(
                value: entry.key,
                child: Text(entry.key),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),

        // Year dropdown
        Expanded(
          child: FilterField(
            label: 'Year *',
            child: AsyncDropdown(
              asyncValue: ref.watch(availableYearsProvider),
              hint: 'Select Year',
              value: filter.yearId,
              onChanged: (val) {
                ref.read(filterProvider.notifier).setYear(val);
                if (val != null) {
                  ref.read(questionFormProvider.notifier).setYear(val);
                }
              },
              itemBuilder: (entry) => DropdownMenuItem(
                value: entry.key,
                child: Text(entry.key),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),

        // Subject dropdown
        Expanded(
          child: FilterField(
            label: 'Subject *',
            child: AsyncDropdown(
              asyncValue: ref.watch(availableSubjectsProvider),
              hint: 'Select Subject',
              value: filter.subjectId,
              onChanged: (val) {
                ref.read(filterProvider.notifier).setSubject(val);
                if (val != null) {
                  ref.read(questionFormProvider.notifier).setSubject(val);
                }
              },
              itemBuilder: (entry) => DropdownMenuItem(
                value: entry.key,
                child: Text(entry.value.name),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),

        // Type dropdown
        Expanded(
          child: FilterField(
            label: 'Type',
            child: DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                hintText: 'Select Type',
              ),
              value: question.type.name, // Use question type directly
              onChanged: (value) {
                if (value != null) {
                  final typeEnum = QuestionType.values.firstWhere(
                    (e) => e.name == value,
                    orElse: () => QuestionType.mcq,
                  );
                  // Only update form state, not filter state
                  ref.read(questionFormProvider.notifier).setType(typeEnum);
                }
              },
              items: QuestionType.values
                  .map((type) => DropdownMenuItem(
                        value: type.name,
                        child: Text(type.name.toCapitalized()),
                      ))
                  .toList(),
            ),
          ),
        ),
        const SizedBox(width: 16),

        // Difficulty dropdown
        Expanded(
          child: FilterField(
            label: 'Difficulty',
            child: DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                hintText: 'Select Difficulty',
              ),
              value: question.difficulty.name, // Use question difficulty directly
              onChanged: (value) {
                if (value != null) {
                  final difficultyEnum = QuestionDifficulty.values.firstWhere(
                    (e) => e.name == value,
                    orElse: () => QuestionDifficulty.easy,
                  );
                  // Only update form state, not filter state
                  ref.read(questionFormProvider.notifier).setDifficulty(difficultyEnum);
                }
              },
              items: QuestionDifficulty.values
                  .map((difficulty) => DropdownMenuItem(
                        value: difficulty.name,
                        child: Text(difficulty.name.toCapitalized()),
                      ))
                  .toList(),
            ),
          ),
        ),
        const SizedBox(width: 16),

        // Status dropdown
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Status',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  hintText: 'Select Status',
                ),
                value: question.status.name, // Use question status directly
                onChanged: (value) {
                  if (value != null) {
                    final statusEnum = QuestionStatus.values.firstWhere(
                      (e) => e.name == value,
                      orElse: () => QuestionStatus.draft,
                    );
                    // Only update form state, not filter state
                    ref.read(questionFormProvider.notifier).setStatus(statusEnum);
                  }
                },
                items: QuestionStatus.values
                    .map((status) => DropdownMenuItem(
                          value: status.name,
                          child: Text(status.name.toCapitalized()),
                        ))
                    .toList(),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class CreateQuestionScreen extends ConsumerStatefulWidget {
  final String? questionId;

  const CreateQuestionScreen({this.questionId, super.key});

  @override
  ConsumerState<CreateQuestionScreen> createState() => _CreateQuestionScreenState();
}

class _CreateQuestionScreenState extends ConsumerState<CreateQuestionScreen> {
  String? questionId;
  bool isLoading = false;
  bool isEditMode = false;
  bool _questionSaved = false;

  @override
  void initState() {
    super.initState();

    isEditMode = widget.questionId != null;
    questionId = widget.questionId ?? ref.read(questionRepositoryProvider).generateQuestionId();

    if (isEditMode) {
      _loadQuestionData();
    }
  }

  Future<void> _loadQuestionData() async {
    setState(() {
      isLoading = true;
    });

    try {
      final questionEntity = await ref.read(questionRepositoryProvider).getQuestion(questionId!);

      if (questionEntity != null) {
        // Initialize filter with question data (only hierarchical fields)
        final filterNotifier = ref.read(filterProvider.notifier);
        filterNotifier.setCourse(questionEntity.courseId);
        filterNotifier.setYear(questionEntity.yearId);
        filterNotifier.setSubject(questionEntity.subjectId);

        // Initialize form with question data
        ref.read(questionFormProvider.notifier).initWithQuestion(questionEntity);

        // Initialize image providers with existing image paths
        if (questionEntity.questionImage != null && questionEntity.questionImage!.isNotEmpty) {
          final questionImageId = createImageIdentifier(questionId!, ImageType.question);
          ref.read(imageProviderProvider(questionImageId).notifier).setInitialPath(questionEntity.questionImage);
        }

        if (questionEntity.answerImage != null && questionEntity.answerImage!.isNotEmpty) {
          final answerImageId = createImageIdentifier(questionId!, ImageType.answer);
          ref.read(imageProviderProvider(answerImageId).notifier).setInitialPath(questionEntity.answerImage);
        }

        // Initialize option images if any
        for (int i = 0; i < questionEntity.optionImages.length; i++) {
          if (questionEntity.optionImages[i].isNotEmpty) {
            final optionImageId = createImageIdentifier(questionId!, ImageType.option(i));
            ref.read(imageProviderProvider(optionImageId).notifier).setInitialPath(questionEntity.optionImages[i]);
          }
        }
      } else {
        debugPrint('Question not found or returned null');
      }
    } catch (e) {
      debugPrint('Error loading question: $e');
      if (mounted) {
        ErrorHandler.showErrorSnackBar(ref, e, context: 'Load Question');
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final question = ref.watch(questionFormProvider);
    final title = isEditMode ? 'Edit Question' : 'Add Question';
    final saveButtonText = isEditMode ? "Update" : "Create";

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        final canPop = await _handleBackNavigation();
        if (canPop && context.mounted) {
          if (Navigator.of(context).canPop()) {
            context.pop();
          } else {
            context.go('/questions');
          }
        }
      },
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          title: Text(title),
          actions: [
            AppButton(label: "Cancel", onPressed: _onCancelPressed, variant: AppButtonVariant.outlined),
            const SizedBox(width: 12),
            AppButton(label: saveButtonText, onPressed: saveQuestion),
            const SizedBox(width: 12),
          ],
        ),
        body: isLoading
            ? const Center(child: CircularProgressIndicator())
            : Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Use custom filter bar that doesn't reset form
                    const QuestionFilterBar(),
                    Expanded(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.only(top: 16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Question text field
                            AppFormField(
                              label: 'Question Text',
                              isRequired: true,
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: TextFormField(
                                      decoration: const InputDecoration(
                                        hintText: 'Enter question text',
                                      ),
                                      maxLines: 3,
                                      initialValue: question.question,
                                      onChanged: (value) {
                                        ref.read(questionFormProvider.notifier).setQuestion(value);
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  ImageSelector(
                                    initialImagePath: question.questionImage,
                                    onImageSelected: (path) {
                                      ref.read(questionFormProvider.notifier).setQuestionImage(path);
                                    },
                                    questionId: questionId!,
                                    type: ImageType.question,
                                  ),
                                ],
                              ),
                            ),

                            const SizedBox(height: 24),

                            // Conditionally show MCQ or FlipCard content
                            if (question.type == QuestionType.flipCard)
                              FlipCardSection(questionId: questionId!)
                            else
                              McqSection(questionId: questionId!),

                            const SizedBox(height: 24),

                            // Explanation field
                            AppFormField(
                              label: 'Explanation',
                              child: TextFormField(
                                decoration: const InputDecoration(
                                  hintText: 'Enter explanation for the answer',
                                ),
                                initialValue: question.explanation ?? '',
                                maxLines: 3,
                                onChanged: (value) {
                                  ref.read(questionFormProvider.notifier).setExplanation(value);
                                },
                              ),
                            ),

                            // Reference field
                            AppFormField(
                              label: 'Reference',
                              child: TextFormField(
                                decoration: const InputDecoration(
                                  hintText: 'Enter reference information',
                                ),
                                initialValue: question.reference ?? '',
                                onChanged: (value) {
                                  ref.read(questionFormProvider.notifier).setReference(value);
                                },
                              ),
                            ),

                            // Keywords field
                            AppFormField(
                              label: 'Search Keywords',
                              child: TextFormField(
                                decoration: const InputDecoration(
                                  hintText: 'Enter comma-separated keywords',
                                ),
                                initialValue: question.keywords.join(', '),
                                onChanged: (value) {
                                  final keywords =
                                      value.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
                                  ref.read(questionFormProvider.notifier).setKeywords(keywords);
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  // Fixed method for cancel button to avoid async gap warning
  void _onCancelPressed() {
    _confirmNavigation();
  }

  // Handles navigation confirmation without BuildContext across async gaps
  Future<void> _confirmNavigation() async {
    final canPop = await _handleBackNavigation();
    if (canPop && mounted) {
      context.pop();
    }
  }

  // Collect all images that have been uploaded or modified
  Map<String, ImageState> _getAllImages() {
    final images = <String, ImageState>{};

    // Add question image
    final questionImageId = createImageIdentifier(questionId!, ImageType.question);
    final questionImageState = ref.read(imageProviderProvider(questionImageId)).valueOrNull;
    if (questionImageState != null) {
      images[ImageType.question] = questionImageState;
    }

    // Add answer image
    final answerImageId = createImageIdentifier(questionId!, ImageType.answer);
    final answerImageState = ref.read(imageProviderProvider(answerImageId)).valueOrNull;
    if (answerImageState != null) {
      images[ImageType.answer] = answerImageState;
    }

    // Add option images
    final question = ref.read(questionFormProvider);
    for (int i = 0; i < question.options.length; i++) {
      final optionType = ImageType.option(i);
      final optionImageId = createImageIdentifier(questionId!, optionType);
      final optionImageState = ref.read(imageProviderProvider(optionImageId)).valueOrNull;
      if (optionImageState != null) {
        images[optionType] = optionImageState;
      }
    }

    return images;
  }

  // Check if there are any unsaved changes
  bool _hasUnsavedChanges() {
    // Check if form has been modified by comparing with original
    final isDirty = ref.read(questionFormProvider.notifier).isDirty;

    if (isDirty) {
      return true;
    }

    // Check if any images have been modified
    final images = _getAllImages();
    for (final imageState in images.values) {
      if (imageState.isDirty) {
        return true;
      }
    }

    return false;
  }

  // Handle back navigation with cleanup
  Future<bool> _handleBackNavigation() async {
    // If already saved, or if no unsaved changes, just allow navigation
    if (_questionSaved || !_hasUnsavedChanges()) {
      return true;
    }

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
          context: context,
          builder: (dialogContext) => AlertDialog(
            title: const Text('Discard Changes?'),
            content: const Text('You have unsaved changes that will be lost. Do you want to continue?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(dialogContext, false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(dialogContext, true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Discard'),
              ),
            ],
          ),
        ) ??
        false;

    if (confirmed) {
      // Clean up any images
      await _cleanupUnusedImages();
    }

    return confirmed;
  }

  // Clean up unused images when discarding changes
  Future<void> _cleanupUnusedImages() async {
    try {
      // Collect all image paths that need to be deleted
      final images = _getAllImages();
      final imagePaths = images.values.where((state) => state.path != null).map((state) => state.path!).toList();

      if (imagePaths.isNotEmpty) {
        await ref.read(questionServiceProvider.notifier).cleanupQuestionImages(questionId!, imagePaths);
      }
    } catch (e) {
      debugPrint('Error cleaning up images: $e');
    }
  }

  // Save the question with enhanced validation
  void saveQuestion() async {
    try {
      // Do client-side validation first before showing loading state
      final question = ref.read(questionFormProvider);

      // Pre-validate required fields to show more user-friendly UI feedback
      String? validationError = _validateQuestionClientSide(question);
      if (validationError != null) {
        // Show validation error directly in snackbar without going through error handler
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(validationError),
          backgroundColor: Colors.red[700],
        ));
        return;
      }

      // Proceed with saving if validation passed
      setState(() {
        isLoading = true;
      });

      final images = _getAllImages();

      // Save question using service (which will do a more thorough validation)
      final success = await ref.read(questionServiceProvider.notifier).saveQuestion(
            questionId!,
            question,
            images,
            isEditMode,
          );

      if (success) {
        _questionSaved = true;

        if (mounted) {
          context.pop(true);
          ErrorHandler.showSuccessSnackBar(
              ref, isEditMode ? 'Question updated successfully' : 'Question created successfully');
        }
      } else {
        final error = ref.read(questionServiceProvider).error;
        if (error != null && mounted) {
          // Display validation errors directly, without error handler transformation
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text(error.toString()),
            backgroundColor: Colors.red[700],
          ));
        }
      }
    } catch (e) {
      debugPrint('Error saving question: $e');
      if (mounted) {
        // Only use ErrorHandler for unexpected errors, not validation errors
        ErrorHandler.showErrorSnackBar(ref, e);
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  // Client-side validation that provides quick feedback before saving
  String? _validateQuestionClientSide(QuestionEntity question) {
    // Check hierarchy fields with user-friendly messages
    if (question.courseId.isEmpty) {
      return 'Please select a Course at the top of the form';
    }

    if (question.yearId.isEmpty) {
      return 'Please select a Year at the top of the form';
    }

    if (question.subjectId.isEmpty) {
      return 'Please select a Subject at the top of the form';
    }

    // Check question content
    if (question.question.isEmpty) {
      return 'Please enter the question text';
    }

    // Type-specific validation with helpful messages
    if (question.type == QuestionType.mcq) {
      // Check for at least two non-empty options
      int nonEmptyOptionsCount = question.options.where((option) => option.isNotEmpty).length;
      if (nonEmptyOptionsCount < 2) {
        return 'Please provide at least 2 options for multiple-choice questions';
      }

      // Check for correct answer selection
      if (question.options[question.correctOptionIndex].isEmpty) {
        return 'The selected correct answer is empty. Please choose a valid option as the correct answer';
      }
    } else if (question.type == QuestionType.flipCard) {
      // Check for answer in flip card
      if (question.answer == null || question.answer!.isEmpty) {
        return 'Please provide an answer for the flip card';
      }
    }

    return null; // No validation errors
  }
}
