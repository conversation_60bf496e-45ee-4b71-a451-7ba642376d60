// lib/features/questions/providers/question_form_provider.dart

import 'package:entities/question_entity.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../shared/providers/filter_provider.dart';

part 'question_form_provider.g.dart';

/// Provider for managing question form state using QuestionEntity directly
@riverpod
class QuestionForm extends _$QuestionForm {
  QuestionEntity? _originalEntity;

  @override
  QuestionEntity build() {
    // Only use filters for initial setup, not for reactivity
    final filter = ref.read(filterProvider);

    // Create default question entity
    return QuestionEntity(
        courseId: filter.courseId ?? '',
        yearId: filter.yearId ?? '',
        subjectId: filter.subjectId ?? '',
        question: '',
        type: filter.type ?? QuestionType.mcq,
        difficulty: filter.difficulty ?? QuestionDifficulty.easy,
        status: filter.status ?? QuestionStatus.draft,
        options: ['', '', ''],
        optionImages: ['', '', '']);
  }

  /// Initialize the form with a question entity
  void initWithQuestion(QuestionEntity question) {
    _originalEntity = question;
    state = question;
  }

  /// Check if the form has unsaved changes
  bool get isDirty {
    if (_originalEntity == null) return false;

    return state != _originalEntity;
  }

  // Filter-related setters (manual, not reactive)
  void updateFromFilter() {
    final filter = ref.read(filterProvider);

    // Only update filters that have actual values
    if (filter.courseId != null) {
      setCourse(filter.courseId!);
    }

    if (filter.yearId != null) {
      setYear(filter.yearId!);
    }

    if (filter.subjectId != null) {
      setSubject(filter.subjectId!);
    }

    if (filter.type != null) {
      setType(filter.type!);
    }

    if (filter.difficulty != null) {
      setDifficulty(filter.difficulty!);
    }

    if (filter.status != null) {
      setStatus(filter.status!);
    }
  }

  // Basic field setters
  void setCourse(String courseId) {
    state = state.copyWith(courseId: courseId);
  }

  void setYear(String yearId) {
    state = state.copyWith(yearId: yearId);
  }

  void setSubject(String subjectId) {
    state = state.copyWith(subjectId: subjectId);
  }

  void setDifficulty(QuestionDifficulty difficulty) {
    state = state.copyWith(difficulty: difficulty);
  }

  void setStatus(QuestionStatus status) {
    state = state.copyWith(status: status);
  }

  void setQuestion(String question) {
    state = state.copyWith(question: question);
  }

  void setQuestionImage(String? imagePath) {
    state = state.copyWith(questionImage: imagePath);
  }

  void setAnswer(String answer) {
    state = state.copyWith(answer: answer);
  }

  void setAnswerImage(String? imagePath) {
    state = state.copyWith(answerImage: imagePath);
  }

  void setType(QuestionType type) {
    // If changing type, we need to handle data conversion
    if (state.type != type) {
      if (state.type == QuestionType.mcq && type == QuestionType.flipCard) {
        _convertMcqToFlipCard(type);
      } else if (state.type == QuestionType.flipCard && type == QuestionType.mcq) {
        _convertFlipCardToMcq(type);
      }
    } else {
      state = state.copyWith(type: type);
    }
  }

  // Helper method to convert from MCQ to FlipCard
  void _convertMcqToFlipCard(QuestionType type) {
    // Get the correct option text and image
    String? correctOptionText;
    String? correctOptionPath;

    if (state.correctOptionIndex >= 0 && state.correctOptionIndex < state.options.length) {
      correctOptionText = state.options[state.correctOptionIndex];

      if (state.correctOptionIndex < state.optionImages.length) {
        final imagePath = state.optionImages[state.correctOptionIndex];
        if (imagePath.isNotEmpty) {
          correctOptionPath = imagePath;
        }
      }
    }

    // Update state with the new type and transfer data
    state = state.copyWith(
      type: type,
      answer: correctOptionText,
      answerImage: correctOptionPath,
    );
  }

  // Helper method to convert from FlipCard to MCQ
  void _convertFlipCardToMcq(QuestionType type) {
    // Create default options structure
    final List<String> newOptions = ['', '', ''];
    final List<String> newOptionImages = ['', '', ''];

    // If we have an answer, use it as the first (correct) option
    if (state.answer != null && state.answer!.isNotEmpty) {
      newOptions[0] = state.answer!;
      if (state.answerImage != null && state.answerImage!.isNotEmpty) {
        newOptionImages[0] = state.answerImage!;
      }
    }

    state = state.copyWith(
      type: type,
      options: newOptions,
      optionImages: newOptionImages,
      correctOptionIndex: 0, // Set first option as correct
    );
  }

  void addOption(String option, [String? imagePath]) {
    final options = List<String>.from(state.options);
    final optionImages = List<String>.from(state.optionImages);

    options.add(option);
    optionImages.add(imagePath ?? '');

    state = state.copyWith(
      options: options,
      optionImages: optionImages,
    );
  }

  void updateOption(int index, String option, [String? imagePath]) {
    final options = List<String>.from(state.options);
    final optionImages = List<String>.from(state.optionImages);

    if (index >= 0 && index < options.length) {
      options[index] = option;

      if (index < optionImages.length && imagePath != null) {
        optionImages[index] = imagePath;
      }

      state = state.copyWith(
        options: options,
        optionImages: optionImages,
      );
    }
  }

  void removeOption(int index) {
    final options = List<String>.from(state.options);
    final optionImages = List<String>.from(state.optionImages);

    if (index >= 0 && index < options.length) {
      options.removeAt(index);
      if (index < optionImages.length) {
        optionImages.removeAt(index);
      }

      // Adjust correctOptionIndex if needed
      int correctIndex = state.correctOptionIndex;
      if (index == correctIndex) {
        // If we're removing the correct option, set the first option as correct
        correctIndex = options.isNotEmpty ? 0 : 0;
      } else if (index < correctIndex) {
        // If we're removing an option before the correct one, decrement the index
        correctIndex--;
      }

      state = state.copyWith(
        options: options,
        optionImages: optionImages,
        correctOptionIndex: correctIndex,
      );
    }
  }

  void setCorrectOptionIndex(int index) {
    if (index >= 0 && index < state.options.length) {
      state = state.copyWith(correctOptionIndex: index);
    }
  }

  void setExplanation(String explanation) {
    state = state.copyWith(explanation: explanation.isNotEmpty ? explanation : null);
  }

  void setReference(String reference) {
    state = state.copyWith(reference: reference.isNotEmpty ? reference : null);
  }

  void setKeywords(List<String> keywords) {
    state = state.copyWith(keywords: keywords);
  }

  /// Reset the form to initial state
  void reset() {
    if (_originalEntity != null) {
      state = _originalEntity!;
    } else {
      final filter = ref.read(filterProvider);

      state = QuestionEntity(
          courseId: filter.courseId ?? '',
          yearId: filter.yearId ?? '',
          subjectId: filter.subjectId ?? '',
          question: '',
          type: filter.type ?? QuestionType.mcq,
          difficulty: filter.difficulty ?? QuestionDifficulty.easy,
          status: filter.status ?? QuestionStatus.draft,
          options: ['', '', ''],
          optionImages: ['', '', '']);
    }
  }
}
