// lib/features/questions/providers/question_image_provider.dart

import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../models/image_state.dart';
import '../services/question_service.dart';

part 'question_image_provider.g.dart';

/// A unified image provider that uses a composite identifier to manage all types of images
@riverpod
class ImageProvider extends _$ImageProvider {
  @override
  AsyncValue<ImageState> build(String identifier) {
    // Initialize with an empty state
    return AsyncData(ImageState.initial());
  }

  /// Parse the identifier to extract questionId and imageType
  Map<String, String> _parseIdentifier(String identifier) {
    final parts = identifier.split('::');
    if (parts.length != 2) {
      throw ArgumentError('Invalid identifier format: $identifier. Expected format: "questionId::imageType"');
    }

    return {
      'questionId': parts[0],
      'imageType': parts[1],
    };
  }

  /// Initialize with an existing image path
  void setInitialPath(String? path) {
    if (path == null || path.isEmpty) return;

    state = AsyncData(ImageState.withPath(path));

    // Optionally load the URL for display
    _loadUrlForDisplay(path);
  }

  /// Helper to load URL for a path (for display)
  Future<void> _loadUrlForDisplay(String path) async {
    try {
      // Get URL directly from Firebase Storage
      final url = await FirebaseStorage.instance.ref(path).getDownloadURL();

      // Make sure the path hasn't changed before updating state
      final currentState = state.valueOrNull;
      if (currentState != null && currentState.path == path) {
        state = AsyncData(ImageState.withPath(path, downloadUrl: url));
      }
    } catch (e) {
      debugPrint('Error loading URL for path $path: $e');
      // Keep the path even if URL loading fails
    }
  }

  /// Upload a new image
  Future<void> uploadImage(XFile imageFile) async {
    // Set uploading state
    state = AsyncData(ImageState.uploading());

    try {
      // Parse the identifier to get questionId and imageType
      final params = _parseIdentifier(identifier);
      final questionId = params['questionId']!;
      final imageType = params['imageType']!;

      // Upload image using service
      final service = ref.read(questionServiceProvider.notifier);
      final imageState = await service.uploadQuestionImage(questionId, imageType, imageFile);

      state = AsyncData(imageState);
    } catch (e) {
      debugPrint('Error uploading image: $e');
      state = AsyncError(e, StackTrace.current);
    }
  }

  /// Delete the image
  Future<void> deleteImage() async {
    final currentState = state.valueOrNull;
    if (currentState == null || currentState.path == null) {
      state = AsyncData(ImageState.initial());
      return;
    }

    // Mark as to be deleted
    state = AsyncData(ImageState.toDelete(currentState.path));

    try {
      // Delete the image using service
      final service = ref.read(questionServiceProvider.notifier);
      await service.deleteQuestionImage(currentState.path);

      // Reset state
      state = AsyncData(ImageState.initial());
    } catch (e) {
      debugPrint('Error deleting image: $e');
      // Still reset the state even if deletion fails
      state = AsyncData(ImageState.initial());
    }
  }

  /// Get the current path
  String? get currentPath => state.valueOrNull?.path;

  /// Get the current URL (if available)
  String? get currentUrl => state.valueOrNull?.downloadUrl;

  /// Whether there's an image
  bool get hasImage => currentPath != null;

  /// Whether the image is marked for deletion
  bool get isMarkedForDeletion => state.valueOrNull?.shouldDelete == true;
}

/// Helper function to create a composite identifier for the image provider
String createImageIdentifier(String questionId, String imageType) {
  return '$questionId::$imageType';
}
