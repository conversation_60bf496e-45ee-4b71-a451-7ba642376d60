// lib/features/questions/services/question_service.dart

import 'package:entities/question_entity.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../models/image_state.dart';
import '../repositories/question_repository.dart';

part 'question_service.g.dart';

/// Service for handling question operations
@riverpod
class QuestionService extends _$QuestionService {
  @override
  Future<void> build() async {
    // Nothing to initialize
  }

  /// Validate a question with comprehensive checks
  String? validateQuestion(QuestionEntity question) {
    // Check hierarchy fields
    if (question.courseId.isEmpty) {
      return 'Please select a Course';
    }

    if (question.yearId.isEmpty) {
      return 'Please select a Year/Module';
    }

    if (question.subjectId.isEmpty) {
      return 'Please select a Subject';
    }

    // Check question content
    if (question.question.isEmpty) {
      return 'Question text is required';
    }

    // Type-specific validation
    if (question.type == QuestionType.mcq) {
      // Check if options exist
      if (question.options.isEmpty) {
        return 'Please add at least one option';
      }

      // Check for empty options
      bool hasNonEmptyOptions = false;
      for (int i = 0; i < question.options.length; i++) {
        if (question.options[i].isNotEmpty) {
          hasNonEmptyOptions = true;
          break;
        }
      }

      if (!hasNonEmptyOptions) {
        return 'At least one option must have content';
      }

      // Check that we have at least two options with content
      int nonEmptyOptionsCount = question.options.where((option) => option.isNotEmpty).length;
      if (nonEmptyOptionsCount < 2) {
        return 'Multiple-choice questions require at least 2 options';
      }

      // Check that correct option index is valid
      if (question.correctOptionIndex >= question.options.length) {
        return 'Please select a correct answer';
      }

      // Check that the selected option has text
      if (question.options[question.correctOptionIndex].isEmpty) {
        return 'The correct option cannot be empty';
      }
    } else if (question.type == QuestionType.flipCard) {
      // Validate FlipCard question
      if (question.answer == null || question.answer!.isEmpty) {
        return 'Answer is required for FlipCard questions';
      }
    }

    return null; // No validation errors
  }

  /// Create or update a question
  Future<bool> saveQuestion(
      String questionId, QuestionEntity question, Map<String, ImageState> images, bool isEditMode) async {
    state = const AsyncLoading();

    try {
      // Validate form
      final validationError = validateQuestion(question);
      if (validationError != null) {
        state = AsyncError(validationError, StackTrace.current);
        return false;
      }

      // Get repository
      final repo = ref.read(questionRepositoryProvider);

      // Process all images first
      final processedImages = await _processImages(images);

      // Create entity with processed images
      final questionWithImages = _updateQuestionWithImages(questionId, question, processedImages, isEditMode);

      // Save to Firestore
      if (isEditMode) {
        await repo.updateQuestion(questionWithImages);
      } else {
        await repo.addQuestion(questionWithImages);
      }

      state = const AsyncData(null);
      return true;
    } catch (e, st) {
      debugPrint('Error saving question: $e');
      state = AsyncError(e, st);
      return false;
    }
  }

  /// Upload an image for a question
  Future<ImageState> uploadQuestionImage(String questionId, String imageType, XFile imageFile) async {
    try {
      final repo = ref.read(questionRepositoryProvider);

      // Upload the image and get path
      final path = await repo.uploadImage(questionId, imageType, imageFile);

      // Get URL directly from Firebase Storage for preview purposes
      String? downloadUrl;
      try {
        downloadUrl = await FirebaseStorage.instance.ref(path).getDownloadURL();
      } catch (e) {
        debugPrint('Error getting URL for uploaded image: $e');
        // Continue without URL - the path is what matters for storage
      }

      // Return the image state
      return ImageState.uploaded(path, downloadUrl: downloadUrl);
    } catch (e) {
      debugPrint('Error uploading image: $e');
      rethrow;
    }
  }

  /// Delete an image for a question
  Future<bool> deleteQuestionImage(String? path) async {
    if (path == null || path.isEmpty) return true;

    try {
      final repo = ref.read(questionRepositoryProvider);
      return await repo.deleteImage(path);
    } catch (e) {
      debugPrint('Error deleting image: $e');
      return false;
    }
  }

  /// Clean up all images for a question
  Future<void> cleanupQuestionImages(String questionId, List<String?> imagePaths) async {
    try {
      final repo = ref.read(questionRepositoryProvider);
      await repo.cleanupAllQuestionImages(questionId, imagePaths);
    } catch (e) {
      debugPrint('Error cleaning up images: $e');
    }
  }

  /// Process all images and return a map of image types to paths
  Future<Map<String, String?>> _processImages(Map<String, ImageState> images) async {
    final repo = ref.read(questionRepositoryProvider);
    final result = <String, String?>{};

    // Process each image
    for (final entry in images.entries) {
      final imageType = entry.key;
      final imageState = entry.value;

      if (imageState.shouldDelete && imageState.path != null) {
        // Delete the image
        await repo.deleteImage(imageState.path);
        result[imageType] = null;
      } else if (imageState.path != null) {
        // Keep the image path
        result[imageType] = imageState.path;
      }
    }

    return result;
  }

  /// Update a question entity with image paths
  QuestionEntity _updateQuestionWithImages(
      String questionId, QuestionEntity question, Map<String, String?> images, bool isEditMode) {
    final now = DateTime.now();

    // Get image paths
    final questionImage = images[ImageType.question];
    final answerImage = images[ImageType.answer];

    // Prepare option images
    final List<String> optionImagePaths = List<String>.from(question.optionImages);
    for (int i = 0; i < question.options.length; i++) {
      final imageKey = ImageType.option(i);
      final imagePath = images[imageKey];

      if (imagePath != null) {
        // Ensure optionImagePaths list is long enough
        while (optionImagePaths.length <= i) {
          optionImagePaths.add('');
        }
        optionImagePaths[i] = imagePath;
      }
    }

    // Create updated question entity
    return question.copyWith(
      id: questionId,
      questionImage: questionImage,
      answerImage: answerImage,
      optionImages: optionImagePaths,
      createdAt: isEditMode ? question.createdAt : now,
      updatedAt: now,
    );
  }
}
