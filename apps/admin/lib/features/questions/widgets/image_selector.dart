// lib/features/questions/widgets/image_selector.dart

import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';

import '../providers/question_image_provider.dart';

class ImageSelector extends ConsumerStatefulWidget {
  final String questionId;
  final String type;
  final Function(String?) onImageSelected;
  final String? initialImagePath;
  final double size;

  const ImageSelector({
    required this.questionId,
    required this.type,
    required this.onImageSelected,
    this.initialImagePath,
    this.size = 100,
    super.key,
  });

  @override
  ConsumerState<ImageSelector> createState() => _ImageSelectorState();
}

class _ImageSelectorState extends ConsumerState<ImageSelector> {
  String? _currentPath;
  String? _currentUrl;
  bool _isLoading = false;
  late String _imageIdentifier;

  @override
  void initState() {
    super.initState();

    // Create the image identifier
    _imageIdentifier = createImageIdentifier(widget.questionId, widget.type);

    // Initialize with path if provided
    if (widget.initialImagePath != null && widget.initialImagePath!.isNotEmpty) {
      _currentPath = widget.initialImagePath;
      _loadUrlFromPath(widget.initialImagePath!);

      // Also initialize the provider
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(imageProviderProvider(_imageIdentifier).notifier).setInitialPath(widget.initialImagePath);
      });
    }
  }

  Future<void> _loadUrlFromPath(String path) async {
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      final url = await FirebaseStorage.instance.ref(path).getDownloadURL();

      if (mounted) {
        setState(() {
          _currentUrl = url;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading URL for path $path: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch the provider for changes during upload or delete
    final imageAsync = ref.watch(imageProviderProvider(_imageIdentifier));

    if (imageAsync.hasValue && imageAsync.value != null) {
      // If provider has a path and we don't, update local state
      if (imageAsync.value!.path != null && _currentPath != imageAsync.value!.path) {
        _currentPath = imageAsync.value!.path;

        if (imageAsync.value!.downloadUrl != null) {
          _currentUrl = imageAsync.value!.downloadUrl;
        } else if (_currentPath != null && _currentPath!.isNotEmpty) {
          // If we have a path but no URL, load it
          _loadUrlFromPath(_currentPath!);
        }
      }

      // If provider has a download URL and we don't, update local state
      if (imageAsync.value!.downloadUrl != null && _currentUrl != imageAsync.value!.downloadUrl) {
        _currentUrl = imageAsync.value!.downloadUrl;
      }

      // If provider indicates image is uploading, update local state
      if (imageAsync.value!.isUploading && !_isLoading) {
        _isLoading = true;
      } else if (!imageAsync.value!.isUploading && _isLoading) {
        _isLoading = false;
      }
    }

    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: _buildContent(),
    );
  }

  Widget _buildContent() {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Show either the image or a placeholder
        if (_isLoading)
          const Center(child: CircularProgressIndicator())
        else if (_currentUrl != null)
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              _currentUrl!,
              width: widget.size,
              height: widget.size,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                debugPrint('Error loading image from URL: $error');
                return _buildImageFallback();
              },
            ),
          )
        else if (_currentPath != null)
          _buildImageFromPath(_currentPath!)
        else
          Icon(
            Icons.image_outlined,
            size: 48,
            color: Colors.grey[400],
          ),

        // Upload button overlay
        Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(8),
            onTap: _selectImage,
            child: Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: (_currentPath != null || _currentUrl != null) ? Colors.black.withAlpha(25) : Colors.transparent,
              ),
            ),
          ),
        ),

        // Delete button if image exists
        if (_currentPath != null || _currentUrl != null)
          Positioned(
            top: 4,
            right: 4,
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                onPressed: _deleteImage,
                constraints: const BoxConstraints(
                  minWidth: 24,
                  minHeight: 24,
                ),
                padding: EdgeInsets.zero,
                iconSize: 20,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildImageFromPath(String path) {
    return FutureBuilder<String>(
      future: FirebaseStorage.instance.ref(path).getDownloadURL(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return _buildImageFallback();
        }

        // Cache the URL for future use
        _currentUrl = snapshot.data;

        return ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.network(
            snapshot.data!,
            width: widget.size,
            height: widget.size,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => _buildImageFallback(),
          ),
        );
      },
    );
  }

  Widget _buildImageFallback() {
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        Icons.broken_image,
        size: 40,
        color: Colors.grey[600],
      ),
    );
  }

  Future<void> _selectImage() async {
    try {
      final picker = ImagePicker();
      final image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (image != null) {
        setState(() {
          _isLoading = true;
        });

        await ref.read(imageProviderProvider(_imageIdentifier).notifier).uploadImage(image);

        // Get the new image path and update form state
        final imageState = ref.read(imageProviderProvider(_imageIdentifier)).valueOrNull;

        widget.onImageSelected(imageState?.path);

        if (mounted) {
          setState(() {
            _currentPath = imageState?.path;
            _currentUrl = imageState?.downloadUrl;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      debugPrint('Error picking image: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking image: $e')),
        );
      }
    }
  }

  void _deleteImage() {
    // Delete the image
    ref.read(imageProviderProvider(_imageIdentifier).notifier).deleteImage();

    // Update form state
    widget.onImageSelected(null);

    // Update local state
    setState(() {
      _currentPath = null;
      _currentUrl = null;
    });
  }
}
