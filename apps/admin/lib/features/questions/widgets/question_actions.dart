// lib/features/tests/widgets/question_section.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/question_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:providers/question_provider.dart';

import '../../../shared/providers/filter_provider.dart';
import '../../../shared/utils/error_handler.dart';
import '../../../shared/widgets/status_messages.dart';
import '../../questions/widgets/questions_list_view.dart';
import '../../tests/providers/test_form_provider.dart';

class QuestionActionMenu extends ConsumerWidget {
  final QuestionEntity question;

  const QuestionActionMenu({
    super.key,
    required this.question,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert),
      onSelected: (value) {
        if (value == 'edit') {
          _editQuestion(context, question);
        } else if (value == 'delete') {
          _deleteQuestion(context, ref, question);
        } else if (value == 'publish') {
          _publishQuestion(ref, question);
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem<String>(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 20),
              SizedBox(width: 8),
              Text('Edit'),
            ],
          ),
        ),
        if (question.status != QuestionStatus.published)
          const PopupMenuItem<String>(
            value: 'publish',
            child: Row(
              children: [
                Icon(Icons.publish, size: 20),
                SizedBox(width: 8),
                Text('Publish'),
              ],
            ),
          ),
        const PopupMenuItem<String>(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 20),
              SizedBox(width: 8),
              Text('Delete'),
            ],
          ),
        ),
      ],
    );
  }

  void _editQuestion(BuildContext context, QuestionEntity question) {
    if (question.id != null) {
      context.push('/edit-question/${question.id}');
    }
  }

  void _deleteQuestion(BuildContext context, WidgetRef ref, QuestionEntity question) {
    if (question.id == null) return;

    final questionNotifier = ref.read(questionProvider.notifier);

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete Question'),
        content: const Text('Are you sure you want to delete this question? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();

              questionNotifier.deleteQuestion(question.id!).then((_) {
                try {
                  ErrorHandler.showSuccessSnackBar(ref, 'Question deleted successfully');
                } catch (e) {
                  debugPrint('Widget disposed, cannot show notification: $e');
                }
              }).catchError((error) {
                try {
                  ErrorHandler.showErrorSnackBar(ref, error, context: 'Delete Question');
                } catch (e) {
                  debugPrint('Widget disposed, cannot show error: $e');
                }
              });
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _publishQuestion(WidgetRef ref, QuestionEntity question) {
    if (question.id == null) return;

    final questionNotifier = ref.read(questionProvider.notifier);

    questionNotifier.publishQuestion(question.id!).then((_) {
      try {
        ErrorHandler.showSuccessSnackBar(ref, 'Question published successfully');
      } catch (e) {
        debugPrint('Widget disposed, cannot show notification: $e');
      }
    }).catchError((error) {
      try {
        ErrorHandler.showErrorSnackBar(ref, error, context: 'Publish Question');
      } catch (e) {
        debugPrint('Widget disposed, cannot show error: $e');
      }
    });
  }
}

class QuestionsSection extends ConsumerWidget {
  const QuestionsSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formState = ref.watch(testFormProvider);
    final filter = ref.watch(filterProvider);

    // Check if all required filters are selected
    final areRequiredFiltersSelected = filter.courseId != null && filter.yearId != null && filter.subjectId != null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select Questions',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        _buildQuestionContent(context, ref, areRequiredFiltersSelected, formState),
      ],
    );
  }

  Widget _buildQuestionContent(
      BuildContext context, WidgetRef ref, bool areRequiredFiltersSelected, dynamic formState) {
    if (!areRequiredFiltersSelected) {
      return const FilterSelectionMessage();
    }

    // Build query for published questions
    final filter = ref.read(filterProvider);
    final query = FirebaseFirestore.instance
        .collection('questions')
        .where('courseId', isEqualTo: filter.courseId)
        .where('yearId', isEqualTo: filter.yearId)
        .where('subjectId', isEqualTo: filter.subjectId)
        .where('status', isEqualTo: 'published') // Always use published questions
        .orderBy('updatedAt', descending: true);

    return SizedBox(
      height: 400, // Constrain height
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Simply show the number of selected questions
          Text(
            'Selected: ${formState.questionIds.length}',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: QuestionsListView(
                query: query,
                showCheckboxes: true,
                selectedIds: formState.questionIds,
                onQuestionSelected: (questionId, isSelected) {
                  if (isSelected) {
                    ref.read(testFormProvider.notifier).addQuestion(questionId);
                  } else {
                    ref.read(testFormProvider.notifier).removeQuestion(questionId);
                  }
                },
                emptyWidget: const EmptyDataMessage(
                  title: "No Published Questions Available",
                  message:
                      "No published questions match your current filters. Try changing your filter criteria or publish some questions first.",
                ),
                pageSize: 10, // Smaller page size for this view
              ),
            ),
          ),
        ],
      ),
    );
  }
}
