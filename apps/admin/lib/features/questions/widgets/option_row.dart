// lib/features/questions/widgets/option_row.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/question_form_provider.dart';
import '../repositories/question_repository.dart';
import 'image_selector.dart';

class OptionRow extends StatelessWidget {
  final int index;
  final String option;
  final String? imagePath;
  final bool isCorrect;
  final String questionId;

  const OptionRow({
    super.key,
    required this.index,
    required this.option,
    this.imagePath,
    required this.isCorrect,
    required this.questionId,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, _) {
        // Create a unique identifier for this option's image
        final imageType = ImageType.option(index);

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4.0),
          child: Row(
            children: [
              Radio<bool>(
                value: true,
                groupValue: isCorrect,
                onChanged: (value) {
                  if (value == true) {
                    ref.read(questionFormProvider.notifier).setCorrectOptionIndex(index);
                  }
                },
              ),
              Expanded(
                child: TextFormField(
                  initialValue: option,
                  decoration: const InputDecoration(
                    hintText: 'Enter option text',
                  ),
                  onChanged: (value) {
                    ref.read(questionFormProvider.notifier).updateOption(index, value);
                  },
                ),
              ),
              const SizedBox(width: 16),
              ImageSelector(
                initialImagePath: imagePath,
                onImageSelected: (path) {
                  ref.read(questionFormProvider.notifier).updateOption(index, option, path);
                },
                questionId: questionId,
                type: imageType,
              ),
            ],
          ),
        );
      },
    );
  }
}
