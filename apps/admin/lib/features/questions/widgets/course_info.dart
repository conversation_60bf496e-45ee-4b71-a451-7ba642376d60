// lib/features/questions/widgets/course_info.dart

import 'package:entities/question_entity.dart';
import 'package:flutter/material.dart';

class CourseInfo extends StatelessWidget {
  final QuestionEntity question;
  final Map<String, dynamic>? courses;
  final Map<String, dynamic>? years;
  final Map<String, dynamic>? subjects;

  const CourseInfo({
    super.key,
    required this.question,
    this.courses,
    this.years,
    this.subjects,
  });

  @override
  Widget build(BuildContext context) {
    String courseText = 'Course: ';
    String yearText = 'Year: ';
    String subjectText = 'Subject: ';

    if (courses != null && question.courseId.isNotEmpty) {
      courseText += courses![question.courseId] ?? question.courseId;
    }

    if (years != null && question.yearId.isNotEmpty) {
      yearText += years![question.yearId] ?? question.yearId;
    }

    if (subjects != null && question.subjectId.isNotEmpty) {
      final subject = subjects![question.subjectId];
      subjectText += subject?.name ?? question.subjectId;
    }

    return RichText(
      text: TextSpan(
        style: const TextStyle(color: Colors.black),
        children: [
          TextSpan(
            text: courseText,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const TextSpan(text: '  '),
          TextSpan(
            text: yearText,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const TextSpan(text: '  '),
          TextSpan(
            text: subjectText,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}
