// lib/features/questions/widgets/question_status_badge.dart

import 'package:entities/question_entity.dart';
import 'package:flutter/material.dart';

class QuestionStatusBadge extends StatelessWidget {
  final QuestionStatus status;
  final double? fontSize;

  const QuestionStatusBadge({
    required this.status, 
    this.fontSize,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        _getStatusText(),
        style: TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.w500,
          color: _getTextColor(),
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (status) {
      case QuestionStatus.draft:
        return Colors.grey[200]!;
      case QuestionStatus.review:
        return Colors.orange[100]!;
      case QuestionStatus.published:
        return Colors.green[100]!;
    }
  }
  
  Color _getTextColor() {
    switch (status) {
      case QuestionStatus.draft:
        return Colors.grey[800]!;
      case QuestionStatus.review:
        return Colors.orange[800]!;
      case QuestionStatus.published:
        return Colors.green[800]!;
    }
  }

  String _getStatusText() {
    switch (status) {
      case QuestionStatus.draft:
        return 'Draft';
      case QuestionStatus.review:
        return 'In Review';
      case QuestionStatus.published:
        return 'Published';
    }
  }
}