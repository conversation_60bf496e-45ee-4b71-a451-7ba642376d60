// lib/features/questions/widgets/image_display.dart

import 'package:flutter/material.dart';
import 'package:firebase_storage/firebase_storage.dart';

class ImageDisplay extends StatelessWidget {
  final String? imagePath;
  final double width;
  final double height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;

  const ImageDisplay({
    Key? key,
    this.imagePath,
    this.width = 100,
    this.height = 100,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (imagePath == null || imagePath!.isEmpty) {
      return _buildEmptyContainer();
    }

    return FutureBuilder<String>(
      future: FirebaseStorage.instance.ref(imagePath).getDownloadURL(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return placeholder ?? _buildLoadingContainer();
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return errorWidget ?? _buildErrorContainer();
        }

        return Image.network(
          snapshot.data!,
          width: width,
          height: height,
          fit: fit,
          errorBuilder: (context, error, stackTrace) =>
          errorWidget ?? _buildErrorContainer(),
        );
      },
    );
  }

  Widget _buildEmptyContainer() {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[300],
      child: const Icon(Icons.image, size: 24),
    );
  }

  Widget _buildLoadingContainer() {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[200],
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildErrorContainer() {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[300],
      child: const Icon(Icons.error_outline, size: 24),
    );
  }
}