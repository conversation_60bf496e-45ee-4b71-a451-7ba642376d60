// lib/features/questions/widgets/flipcard_section.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../shared/widgets/app_form_field.dart';
import '../providers/question_form_provider.dart';
import 'image_selector.dart';

class FlipCardSection extends ConsumerWidget {
  final String questionId;

  const FlipCardSection({required this.questionId, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final question = ref.watch(questionFormProvider);

    return AppFormField(
      label: 'Answer',
      isRequired: true,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: TextFormField(
              decoration: const InputDecoration(hintText: 'Enter answer text'),
              initialValue: question.answer ?? '',
              maxLines: 3,
              onChanged: (value) => ref.read(questionFormProvider.notifier).setAnswer(value),
            ),
          ),
          const SizedBox(width: 16),
          ImageSelector(
            initialImagePath: question.answerImage,
            onImageSelected: (path) => ref.read(questionFormProvider.notifier).setAnswerImage(path),
            questionId: questionId,
            type: 'answer',
          ),
        ],
      ),
    );
  }
}
