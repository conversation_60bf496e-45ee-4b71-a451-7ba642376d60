// lib/features/questions/widgets/mcq_section.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/question_form_provider.dart';
import 'option_row.dart';

class McqSection extends ConsumerWidget {
  final String questionId;

  const McqSection({required this.questionId, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final question = ref.watch(questionFormProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Options *', style: TextStyle(fontWeight: FontWeight.w500)),
        const SizedBox(height: 8),
        for (int i = 0; i < question.options.length; i++)
          OptionRow(
            index: i,
            option: question.options[i],
            imagePath: i < question.optionImages.length && question.optionImages[i].isNotEmpty
                ? question.optionImages[i]
                : null,
            isCorrect: question.correctOptionIndex == i,
            questionId: questionId,
          ),
        const SizedBox(height: 8),
        TextButton.icon(
          icon: const Icon(Icons.add),
          label: const Text('Add new option'),
          onPressed: () {
            ref.read(questionFormProvider.notifier).addOption('');
          },
        ),
      ],
    );
  }
}
