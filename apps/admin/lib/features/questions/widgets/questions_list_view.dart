// lib/features/questions/widgets/questions_list_view.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/question_entity.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_ui_firestore/firebase_ui_firestore.dart';
import 'package:flutter/material.dart';
import 'package:providers/common.dart';

import '../../../shared/widgets/status_messages.dart';
import 'question_status_badge.dart';

class QuestionsListView extends StatelessWidget {
  final Query<Map<String, dynamic>> query;
  final bool showCheckboxes;
  final List<String> selectedIds;
  final Function(String, bool)? onQuestionSelected;
  final Widget? emptyWidget;
  final bool showQuestionImage;
  final int pageSize;

  const QuestionsListView({
    super.key,
    required this.query,
    this.showCheckboxes = false,
    this.selectedIds = const [],
    this.onQuestionSelected,
    this.emptyWidget,
    this.showQuestionImage = true,
    this.pageSize = 20,
  });

  @override
  Widget build(BuildContext context) {
    return FirestoreListView<Map<String, dynamic>>(
      query: query,
      pageSize: pageSize,
      loadingBuilder: (context) => const LoadingMessage(message: "Loading questions..."),
      errorBuilder: (context, error, stackTrace) {
        dbgPrint("There was a problem retrieving the questions: ${error.toString()}");
        return ErrorMessage(
          title: "Error Loading Questions",
          message: "There was a problem retrieving the questions",
          onRetry: () {}, // FirestoreListView handles retries internally
        );
      },
      emptyBuilder: (context) =>
          emptyWidget ??
          const EmptyDataMessage(
            title: "No Questions Found",
            message: "No questions match your current criteria.",
          ),
      itemBuilder: (context, snapshot) {
        final question = QuestionEntity.fromJson({
          ...snapshot.data(),
          'id': snapshot.id,
        });

        if (showCheckboxes) {
          final isSelected = selectedIds.contains(question.id);
          return CheckboxListTile(
            title: Text(
              question.question,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Text(
              'Type: ${question.type.name} • Difficulty: ${question.difficulty.name}',
              style: const TextStyle(fontSize: 12),
            ),
            value: isSelected,
            onChanged: (value) {
              if (onQuestionSelected != null && question.id != null) {
                onQuestionSelected!(question.id!, value ?? false);
              }
            },
            secondary: showQuestionImage ? _buildQuestionImage(question.questionImage) : null,
          );
        } else {
          return _buildQuestionItem(question);
        }
      },
    );
  }

  Widget _buildQuestionItem(QuestionEntity question) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: ListTile(
        leading: showQuestionImage ? _buildQuestionImage(question.questionImage) : null,
        title: Text(
          question.question,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          'Type: ${question.type.name} • Difficulty: ${question.difficulty.name}',
        ),
        trailing: QuestionStatusBadge(status: question.status),
      ),
    );
  }

  Widget _buildQuestionImage(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) {
      return SizedBox(
        width: 40,
        height: 40,
        child: Container(
          color: Colors.grey[300],
          child: const Icon(Icons.image, size: 20),
        ),
      );
    }

    return SizedBox(
      width: 40,
      height: 40,
      child: FutureBuilder<String>(
        future: FirebaseStorage.instance.ref(imagePath).getDownloadURL(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Container(
              color: Colors.grey[200],
              child: const Center(
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            );
          }

          if (snapshot.hasError || !snapshot.hasData) {
            return Container(
              color: Colors.grey[300],
              child: const Icon(Icons.error_outline, size: 20),
            );
          }

          return ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: Image.network(
              snapshot.data!,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                color: Colors.grey[300],
                child: const Icon(Icons.error, size: 20),
              ),
            ),
          );
        },
      ),
    );
  }
}
