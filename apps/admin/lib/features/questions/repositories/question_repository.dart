// lib/features/questions/repositories/question_repository.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/question_entity.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'question_repository.g.dart';

/// Constants for image types
class ImageType {
  static const String question = 'question';
  static const String answer = 'answer';
  static const String optionPrefix = 'option_';

  /// Generate option image type with index
  static String option(int index) => '$optionPrefix$index';
}

/// Repository for managing questions and their images
class QuestionRepository {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;

  QuestionRepository({
    FirebaseFirestore? firestore,
    FirebaseStorage? storage,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _storage = storage ?? FirebaseStorage.instance;

  // Collection reference
  CollectionReference<Map<String, dynamic>> get _questions => _firestore.collection('questions');

  // Generate new document ID
  String generateQuestionId() => _questions.doc().id;

  // CRUD operations
  Future<void> addQuestion(QuestionEntity question) async {
    final questionData = question.toJson();
    questionData.remove('id'); // Firestore will add id as doc reference

    await _questions.doc(question.id).set({
      ...questionData,
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }

  Future<void> updateQuestion(QuestionEntity question) async {
    final questionData = question.toJson();
    questionData.remove('id');

    await _questions.doc(question.id).update({
      ...questionData,
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }

  Future<QuestionEntity?> getQuestion(String id) async {
    final doc = await _questions.doc(id).get();

    if (!doc.exists) return null;

    final data = doc.data()!;
    data['id'] = doc.id;

    return QuestionEntity.fromJson(data);
  }

  // Query operations
  Future<List<QuestionEntity>> getQuestions({
    String? courseId,
    String? yearId,
    String? subjectId,
    QuestionType? type,
    QuestionDifficulty? difficulty,
    QuestionStatus? status,
    int limit = 100,
    DocumentSnapshot? startAfter,
  }) async {
    Query<Map<String, dynamic>> query = _questions;

    // Apply filters if provided
    if (courseId != null) {
      query = query.where('courseId', isEqualTo: courseId);
    }

    if (yearId != null) {
      query = query.where('yearId', isEqualTo: yearId);
    }

    if (subjectId != null) {
      query = query.where('subjectId', isEqualTo: subjectId);
    }

    if (type != null) {
      query = query.where('type', isEqualTo: type.toString().split('.').last);
    }

    if (difficulty != null) {
      query = query.where('difficulty', isEqualTo: difficulty.toString().split('.').last);
    }

    if (status != null) {
      query = query.where('status', isEqualTo: status.toString().split('.').last);
    }

    // Apply sorting and pagination
    query = query.orderBy('updatedAt', descending: true).limit(limit);

    if (startAfter != null) {
      query = query.startAfterDocument(startAfter);
    }

    // Execute query
    final querySnapshot = await query.get();

    // Convert to entities
    return querySnapshot.docs.map((doc) {
      final data = doc.data();
      data['id'] = doc.id;
      return QuestionEntity.fromJson(data);
    }).toList();
  }

  // Core image operations
  Future<String> uploadImage(String questionId, String imageType, XFile image) async {
    // Generate storage path
    final String path = _generateImagePath(questionId, imageType, image.name);

    // Upload to Firebase Storage
    final storageRef = _storage.ref().child(path);
    final uploadTask = storageRef.putData(
      await image.readAsBytes(),
      SettableMetadata(contentType: 'image/${image.name.split('.').last}'),
    );

    // Wait for upload to complete
    await uploadTask;

    // Return the storage path
    return path;
  }

  // Delete an image
  Future<bool> deleteImage(String? path) async {
    if (path == null || path.isEmpty) return true;

    try {
      await _storage.ref(path).delete();
      return true;
    } catch (e) {
      debugPrint('Error deleting image: $e');
      // Return true so that the UI can proceed even if deletion fails
      return true;
    }
  }

  /// Generate Firebase Storage path for an image
  String _generateImagePath(String questionId, String imageType, String fileName) {
    final extension = fileName.split('.').last;

    if (imageType == ImageType.question) {
      return 'questions/$questionId/question.$extension';
    } else if (imageType == ImageType.answer) {
      return 'questions/$questionId/answer.$extension';
    } else if (imageType.startsWith(ImageType.optionPrefix)) {
      final optionIndex = imageType.split('_')[1];
      return 'questions/$questionId/option_$optionIndex.$extension';
    } else {
      throw Exception('Unknown image type: $imageType');
    }
  }

  /// Clean up all images for a question that's being deleted or discarded
  Future<void> cleanupAllQuestionImages(String questionId, List<String?> imagePaths) async {
    for (final path in imagePaths) {
      if (path != null && path.isNotEmpty) {
        await deleteImage(path);
      }
    }
  }
}

/// Provider for QuestionRepository
@riverpod
QuestionRepository questionRepository(Ref ref) {
  return QuestionRepository();
}
