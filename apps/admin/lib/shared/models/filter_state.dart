// lib/features/common/models/filter_state.dart

import 'package:entities/question_entity.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'filter_state.freezed.dart';
part 'filter_state.g.dart';

@freezed
class FilterState with _$FilterState {
  factory FilterState({
    String? courseId,
    String? yearId,
    String? subjectId,
    QuestionType? type,
    QuestionDifficulty? difficulty,
    QuestionStatus? status,
  }) = _FilterState;

  factory FilterState.fromJson(Map<String, dynamic> json) =>
      _$FilterStateFromJson(json);
}
