import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/common.dart';
import 'package:providers/firebase_app_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'firebase_user_provider.g.dart';

@Riverpod(keepAlive: true)
Stream<User?> firebaseUser(Ref ref) async* {
  final f = 'firebaseUserProvider';
  dbgPrint('$f: init waiting for FirebaseApp');
  await ref.watch(firebaseAppProvider.future);
  dbgPrint('$f: init ${FirebaseAuth.instance.currentUser}');
  var lastUid = FirebaseAuth.instance.currentUser?.uid;
  yield FirebaseAuth.instance.currentUser;
  await for (final user in FirebaseAuth.instance.authStateChanges()) {
    if (lastUid != user?.uid) {
      dbgPrint('$f: state change to $user');
      lastUid = FirebaseAuth.instance.currentUser?.uid;
      yield user;
    }
  }
  dbgPrint('$f: done');
}
