// apps/admin/lib/config/routes.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../features/auth/screens/forgot_password_screen.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/dashboard/screens/dashboard_screen.dart';
import '../../features/questions/screens/create_question_screen.dart';
import '../../features/questions/screens/questions_screen.dart';
import '../../features/tests/screens/create_test_screen.dart';
import '../../features/tests/screens/tests_screen.dart';
import 'firebase_user_provider.dart';

part 'app_router_provider.g.dart';

class RouterNotifier extends ChangeNotifier {
  RouterNotifier(this.ref) {
    ref.listen(firebaseUserProvider, (_, __) {
      notifyListeners();
    });
  }

  final Ref ref;
}

@riverpod
GoRouter appRouter(Ref ref) {
  final notifier = RouterNotifier(ref);

  return GoRouter(
    initialLocation: '/',
    redirect: (context, state) {
      final user = ref.read(firebaseUserProvider).valueOrNull;
      final isAuthenticated = user != null;
      final isLoginRoute = state.matchedLocation == '/';
      final isForgotPasswordRoute = state.matchedLocation == '/forgot-password';

      // If not logged in and not on auth routes, redirect to login
      if (!isAuthenticated && !isLoginRoute && !isForgotPasswordRoute) {
        return '/';
      }

      // If logged in and on auth routes, redirect to questions (default dashboard page)
      if (isAuthenticated && (isLoginRoute || isForgotPasswordRoute)) {
        return '/questions';
      }

      return null;
    },
    refreshListenable: notifier,
    routes: [
      // Auth routes
      GoRoute(
        path: '/',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),

      // Dashboard Shell Route
      ShellRoute(
        builder: (context, state, child) => DashboardScreen(child: child),
        routes: [
          GoRoute(
            path: '/questions',
            builder: (context, state) => const QuestionsScreen(),
          ),
          GoRoute(
            path: '/add-questions',
            builder: (context, state) => const CreateQuestionScreen(),
          ),
          GoRoute(
            path: '/edit-question/:id',
            builder: (context, state) {
              final questionId = state.pathParameters['id'];
              return CreateQuestionScreen(questionId: questionId);
            },
          ),
          GoRoute(
            path: '/tests',
            builder: (context, state) => const TestsScreen(),
          ),
          GoRoute(
            path: '/add-test',
            builder: (context, state) => const CreateTestScreen(),
          ),
          GoRoute(
            path: '/edit-test/:id',
            builder: (context, state) {
              final testId = state.pathParameters['id'];
              return CreateTestScreen(testId: testId);
            },
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Page not found'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => context.go('/login'),
                child: const Text('Go to Login'),
              ),
            ],
          ),
        ),
      );
    },
  );
}
