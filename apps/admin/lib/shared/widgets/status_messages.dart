// lib/shared/widgets/status_messages.dart

import 'package:flutter/material.dart';

class FilterSelectionMessage extends StatelessWidget {
  const FilterSelectionMessage({super.key});

  @override
  Widget build(BuildContext context) {
    return const MessageDisplay(
      icon: Icons.filter_list,
      title: "Select Filters",
      message: "Please select Course, Year, and Subject to continue.",
    );
  }
}

class LoadingMessage extends StatelessWidget {
  final String message;

  const LoadingMessage({this.message = "Loading...", super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [const CircularProgressIndicator(), const SizedBox(height: 16), Text(message)],
      ),
    );
  }
}

class ErrorMessage extends StatelessWidget {
  final VoidCallback onRetry;
  final String title;
  final String message;

  const ErrorMessage({
    required this.onRetry,
    this.title = "Error Loading Data",
    this.message = "There was a problem retrieving the data. Please try again later.",
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return MessageDisplay(
      icon: Icons.error_outline,
      title: title,
      message: message,
      showRetryButton: true,
      onRetry: onRetry,
    );
  }
}

class EmptyDataMessage extends StatelessWidget {
  final String title;
  final String message;

  const EmptyDataMessage({
    this.title = "No Data Found",
    this.message = "No items match your current filters. Try changing your filter criteria or add a new item.",
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return MessageDisplay(
      icon: Icons.search_off,
      title: title,
      message: message,
    );
  }
}

class MessageDisplay extends StatelessWidget {
  final IconData icon;
  final String title;
  final String message;
  final bool showRetryButton;
  final VoidCallback? onRetry;

  const MessageDisplay({
    required this.icon,
    required this.title,
    required this.message,
    this.showRetryButton = false,
    this.onRetry,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
            ),
            if (showRetryButton && onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(
                  Icons.refresh,
                  color: Colors.white,
                ),
                label: const Text("Retry"),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
