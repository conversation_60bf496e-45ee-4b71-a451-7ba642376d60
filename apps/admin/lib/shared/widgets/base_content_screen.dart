// lib/features/common/widgets/base_content_screen.dart

import 'package:admin/shared/widgets/app_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'filter_bar.dart';

class BaseContentScreen extends ConsumerWidget {
  final String title;
  final String addButtonText;
  final VoidCallback? onImport;
  final VoidCallback onAdd;
  final Widget dataTable;
  final Widget pagination;

  const BaseContentScreen({
    super.key,
    required this.title,
    required this.addButtonText,
    this.onImport,
    required this.onAdd,
    required this.dataTable,
    required this.pagination,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  AppButton(
                    label: "Import",
                    onPressed: onImport,
                    icon: Icons.upload_file,
                    variant: AppButtonVariant.outlined,
                  ),

                  const SizedBox(width: 12),
                  // Add button
                  Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: TextButton.icon(
                      onPressed: onAdd,
                      icon: const Icon(
                        Icons.add,
                        color: Colors.white,
                      ),
                      label: Text(
                        addButtonText,
                        style: const TextStyle(
                          color: Colors.white,
                        ),
                      ),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                        backgroundColor: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Card(
              color: Colors.white,
              elevation: 2,
              margin: EdgeInsets.zero,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: const FilterBar(),
                  ),
                  Expanded(
                    child: dataTable,
                  ),
                  pagination,
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
