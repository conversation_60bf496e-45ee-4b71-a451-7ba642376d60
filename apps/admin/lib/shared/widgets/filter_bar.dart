// lib/features/common/widgets/filter_bar.dart

import 'package:admin/shared/utils/string_extensions.dart';
import 'package:entities/question_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/filter_helpers_provider.dart';
import '../providers/filter_provider.dart';

class FilterBar extends ConsumerWidget {
  const FilterBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filter = ref.watch(filterProvider);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // Course dropdown
        Expanded(
          child: FilterField(
            label: 'Course *',
            child: AsyncDropdown(
              asyncValue: ref.watch(availableCoursesProvider),
              hint: 'Select Course',
              value: filter.courseId,
              onChanged: (val) => ref.read(filterProvider.notifier).setCourse(val),
              itemBuilder: (entry) => DropdownMenuItem(
                value: entry.key,
                child: Text(entry.key),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),

        // Year dropdown
        Expanded(
          child: Filter<PERSON>ield(
            label: 'Year *',
            child: AsyncDropdown(
              asyncValue: ref.watch(availableYearsProvider),
              hint: 'Select Year',
              value: filter.yearId,
              onChanged: (val) => ref.read(filterProvider.notifier).setYear(val),
              itemBuilder: (entry) => DropdownMenuItem(
                value: entry.key,
                child: Text(entry.key),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),

        // Subject dropdown
        Expanded(
          child: FilterField(
            label: 'Subject *',
            child: AsyncDropdown(
              asyncValue: ref.watch(availableSubjectsProvider),
              hint: 'Select Subject',
              value: filter.subjectId,
              onChanged: (val) => ref.read(filterProvider.notifier).setSubject(val),
              itemBuilder: (entry) => DropdownMenuItem(
                value: entry.key,
                child: Text(entry.value.name),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),

        // Type dropdown
        Expanded(
          child: FilterField(
            label: 'Type',
            child: DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                hintText: 'Select Type',
              ),
              value: filter.type?.name,
              onChanged: (value) {
                if (value != null) {
                  final typeEnum = QuestionType.values.firstWhere(
                    (e) => e.name == value,
                    orElse: () => QuestionType.mcq,
                  );
                  ref.read(filterProvider.notifier).setType(typeEnum);
                } else {
                  ref.read(filterProvider.notifier).setType(null);
                }
              },
              items: QuestionType.values
                  .map((type) => DropdownMenuItem(
                        value: type.name,
                        child: Text(type.name.toCapitalized()),
                      ))
                  .toList(),
            ),
          ),
        ),
        const SizedBox(width: 16),

        // Difficulty dropdown
        Expanded(
          child: FilterField(
            label: 'Difficulty',
            child: DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                hintText: 'Select Difficulty',
              ),
              value: filter.difficulty?.name,
              onChanged: (value) {
                if (value != null) {
                  final difficultyEnum = QuestionDifficulty.values.firstWhere(
                    (e) => e.name == value,
                    orElse: () => QuestionDifficulty.easy,
                  );
                  ref.read(filterProvider.notifier).setDifficulty(difficultyEnum);
                } else {
                  ref.read(filterProvider.notifier).setDifficulty(null);
                }
              },
              items: QuestionDifficulty.values
                  .map((difficulty) => DropdownMenuItem(
                        value: difficulty.name,
                        child: Text(difficulty.name.toCapitalized()),
                      ))
                  .toList(),
            ),
          ),
        ),
        const SizedBox(width: 16),

        // Status dropdown
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Status',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  hintText: 'Select Status',
                ),
                value: filter.status?.name,
                onChanged: (value) {
                  if (value != null) {
                    final statusEnum = QuestionStatus.values.firstWhere(
                      (e) => e.name == value,
                      orElse: () => QuestionStatus.draft,
                    );
                    ref.read(filterProvider.notifier).setStatus(statusEnum);
                  } else {
                    ref.read(filterProvider.notifier).setStatus(null);
                  }
                },
                items: QuestionStatus.values
                    .map((status) => DropdownMenuItem(
                          value: status.name,
                          child: Text(status.name.toCapitalized()),
                        ))
                    .toList(),
              ),
            ],
          ),
        ),

        // Search button
        // SizedBox(
        //   height: 48,
        //   child: IconButton(
        //     icon: const Icon(Icons.search),
        //     onPressed: () {
        //       // Trigger search function
        //     },
        //     tooltip: 'Search',
        //   ),
        // ),
      ],
    );
  }
}

class FilterField extends StatelessWidget {
  final String label;
  final Widget child;

  const FilterField({
    super.key,
    required this.label,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        child,
      ],
    );
  }
}

class AsyncDropdown extends ConsumerWidget {
  final AsyncValue<Map<String, dynamic>> asyncValue;
  final String hint;
  final String? value;
  final ValueChanged<String?> onChanged;
  final DropdownMenuItem<String> Function(MapEntry<String, dynamic>) itemBuilder;

  const AsyncDropdown({
    super.key,
    required this.asyncValue,
    required this.hint,
    required this.value,
    required this.onChanged,
    required this.itemBuilder,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (asyncValue.isLoading) {
      return const SizedBox(
        height: 48,
        child: Center(child: CircularProgressIndicator(strokeWidth: 2)),
      );
    }

    if (asyncValue.hasError) {
      return const SizedBox(
        height: 48,
        child: Center(
          child: Text(
            'Unable to load data',
            style: TextStyle(color: Colors.red),
          ),
        ),
      );
    }

    final data = asyncValue.value ?? {};
    final items = data.entries.map(itemBuilder).toList();

    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        hintText: hint,
      ),
      value: value,
      onChanged: onChanged,
      items: items,
    );
  }
}
