// lib/features/dashboard/widgets/app_navigation_bar.dart

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class AppNavigationBar extends StatelessWidget {
  const AppNavigationBar({super.key});

  @override
  Widget build(BuildContext context) {
    final currentLocation = GoRouterState.of(context).matchedLocation;

    return Container(
      color: const Color(0xFF454FBF),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Row(
          children: [
            _NavItem(
              icon: Icons.question_answer_outlined,
              label: 'Questions',
              isSelected: currentLocation.startsWith('/questions'),
              onTap: () => context.go('/questions'),
            ),
            _NavItem(
              icon: Icons.assignment_outlined,
              label: 'Tests',
              isSelected: currentLocation.startsWith('/tests'),
              onTap: () => context.go('/tests'),
            ),
            _NavItem(
              icon: Icons.people_outline,
              label: 'Users',
              isSelected: currentLocation.startsWith('/users'),
              onTap: () => context.go('/users'),
            ),
            _NavItem(
              icon: Icons.bar_chart_outlined,
              label: 'Reports',
              isSelected: currentLocation.startsWith('/reports'),
              onTap: () => context.go('/reports'),
            ),
            _NavItem(
              icon: Icons.insights_outlined,
              label: 'Statistics',
              isSelected: currentLocation.startsWith('/statistics'),
              onTap: () => context.go('/statistics'),
            ),
            _NavItem(
              icon: Icons.notifications_outlined,
              label: 'Notifications',
              isSelected: currentLocation.startsWith('/notifications'),
              onTap: () => context.go('/notifications'),
            ),
          ],
        ),
      ),
    );
  }
}

class _NavItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final bool isSelected;
  final VoidCallback onTap;

  const _NavItem({
    required this.icon,
    required this.label,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
        child: Row(
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: Colors.white,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
