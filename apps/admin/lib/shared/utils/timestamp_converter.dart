// lib/shared/utils/timestamp_converter.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

class TimestampConverter implements JsonConverter<DateTime?, dynamic> {
  const TimestampConverter();

  @override
  DateTime? from<PERSON>son(dynamic data) {
    return switch (data) {
      null => null,
      String s => DateTime.parse(s),
      Timestamp t => t.toDate(),
      _ => () {
          throw ArgumentError(
              'Unsupported type for TimestampConverter: ${data.runtimeType}');
        }()
    };
  }

  @override
  Timestamp? toJson(DateTime? dateTime) {
    if (dateTime == null) {
      return null;
    }
    final timestamp = Timestamp.fromDate(dateTime);
    return timestamp;
  }
}

class TimestampConverterNN implements JsonConverter<DateTime, dynamic> {
  const TimestampConverterNN();

  @override
  DateTime fromJson(dynamic data) {
    return switch (data) {
      String s => DateTime.parse(s),
      Timestamp t => t.toDate(),
      _ => () {
          throw ArgumentError(
              'Unsupported type for TimestampConverterNN: ${data.runtimeType}');
        }()
    };
  }

  @override
  Timestamp toJson(DateTime dateTime) {
    final timestamp = Timestamp.fromDate(dateTime);
    return timestamp;
  }
}
