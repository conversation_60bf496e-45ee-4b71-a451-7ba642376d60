// lib/shared/utils/firestore_query_helpers.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/question_entity.dart';

class FirestoreQueryHelpers {
  /// Build a query for questions with filters
  static Query<Map<String, dynamic>> buildQuestionsQuery({
    String? courseId,
    String? yearId,
    String? subjectId,
    QuestionType? type,
    QuestionDifficulty? difficulty,
    QuestionStatus? status,
    bool publishedOnly = false,
  }) {
    Query<Map<String, dynamic>> query = FirebaseFirestore.instance.collection('questions');

    // Apply filters if provided
    if (courseId != null) {
      query = query.where('courseId', isEqualTo: courseId);
    }

    if (yearId != null) {
      query = query.where('yearId', isEqualTo: yearId);
    }

    if (subjectId != null) {
      query = query.where('subjectId', isEqualTo: subjectId);
    }

    if (type != null) {
      query = query.where('type', isEqualTo: type.name.toLowerCase());
    }

    if (difficulty != null) {
      query = query.where('difficulty', isEqualTo: difficulty.name.toLowerCase());
    }

    // If publishedOnly is true, always use published status
    if (publishedOnly) {
      query = query.where('status', isEqualTo: 'published');
    } else if (status != null) {
      query = query.where('status', isEqualTo: status.name.toLowerCase());
    }

    // Order by updated time by default
    return query.orderBy('updatedAt', descending: true);
  }

  /// Build a query for tests with filters
  static Query<Map<String, dynamic>> buildTestsQuery({
    String? courseId,
    String? yearId,
    String? subjectId,
    QuestionType? type,
    QuestionDifficulty? difficulty,
    QuestionStatus? status,
  }) {
    Query<Map<String, dynamic>> query = FirebaseFirestore.instance.collection('tests');

    // Apply filters if provided
    if (courseId != null) {
      query = query.where('courseId', isEqualTo: courseId);
    }

    if (yearId != null) {
      query = query.where('yearId', isEqualTo: yearId);
    }

    if (subjectId != null) {
      query = query.where('subjectId', isEqualTo: subjectId);
    }

    if (type != null) {
      query = query.where('type', isEqualTo: type.name.toLowerCase());
    }

    if (difficulty != null) {
      query = query.where('difficulty', isEqualTo: difficulty.name.toLowerCase());
    }

    if (status != null) {
      query = query.where('status', isEqualTo: status.name.toLowerCase());
    }

    // Order by updated time by default
    return query.orderBy('updatedAt', descending: true);
  }
}
