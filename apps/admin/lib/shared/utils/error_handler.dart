// lib/shared/utils/error_handler.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/scaffold_messenger_key_provider.dart';

class ErrorHandler {
  // Authentication error messages
  static String handleAuthError(dynamic error) {
    debugPrint('Auth error: $error');
    final errorString = error.toString().toLowerCase();

    // User account issues
    if (errorString.contains('user-not-found')) {
      return 'No account found with this email. Please check your email or create a new account.';
    } else if (errorString.contains('user-disabled')) {
      return 'This account has been disabled. Please contact support for assistance.';
    } else if (errorString.contains('user-token-expired')) {
      return 'Your session has expired. Please sign in again.';
    }

    // Password issues
    else if (errorString.contains('wrong-password')) {
      return 'Incorrect password. Please try again or reset your password.';
    } else if (errorString.contains('weak-password')) {
      return 'The password provided is too weak. Please choose a stronger password.';
    }

    // Email issues
    else if (errorString.contains('invalid-email')) {
      return 'Please enter a valid email address.';
    } else if (errorString.contains('email-already-in-use')) {
      return 'An account already exists with this email address.';
    }

    // Credential issues
    else if (errorString.contains('invalid-credential')) {
      return 'The credentials provided are invalid. Please try again.';
    } else if (errorString.contains('account-exists-with-different-credential')) {
      return 'An account already exists with the same email but different sign-in credentials. Try signing in using a different method.';
    }

    // Session issues
    else if (errorString.contains('session-expired') || errorString.contains('id-token-expired')) {
      return 'Your session has expired. Please sign in again.';
    }

    // Rate limiting/security issues
    else if (errorString.contains('too-many-requests')) {
      return 'Too many failed login attempts. Please try again later or reset your password.';
    }

    // User interaction issues
    else if (errorString.contains('popup-closed-by-user') || errorString.contains('cancelled-by-user')) {
      return 'Sign-in process was cancelled. Please try again.';
    } else if (errorString.contains('operation-not-allowed')) {
      return 'This operation is not allowed. Please contact support.';
    }

    // Network issues
    else if (errorString.contains('network-request-failed')) {
      return 'Network connection issue. Please check your internet connection.';
    } else if (errorString.contains('timeout')) {
      return 'The request timed out. Please try again.';
    }

    // Default authentication error message
    return 'Authentication failed. Please try again.';
  }

  // Firebase/Firestore error messages
  static String handleFirestoreError(dynamic error) {
    debugPrint('Firestore error: $error');
    final errorString = error.toString().toLowerCase();

    // Permission issues
    if (errorString.contains('permission-denied')) {
      return 'You don\'t have permission to perform this action.';
    }

    // Availability issues
    else if (errorString.contains('unavailable')) {
      return 'Service temporarily unavailable. Please try again later.';
    } else if (errorString.contains('deadline-exceeded')) {
      return 'The operation timed out. Please try again.';
    }

    // Resource issues
    else if (errorString.contains('not-found')) {
      return 'The requested resource was not found.';
    } else if (errorString.contains('already-exists')) {
      return 'This item already exists.';
    }

    // Data issues
    else if (errorString.contains('invalid-argument')) {
      return 'Invalid data provided. Please check your inputs.';
    } else if (errorString.contains('failed-precondition')) {
      return 'The operation cannot be performed in the current state.';
    } else if (errorString.contains('out-of-range')) {
      return 'The operation was attempted past the valid range.';
    }

    // Quota issues
    else if (errorString.contains('resource-exhausted')) {
      return 'Resource quota has been exceeded. Please try again later.';
    }

    // Client issues
    else if (errorString.contains('unauthenticated')) {
      return 'You need to be signed in to perform this action.';
    } else if (errorString.contains('aborted')) {
      return 'The operation was aborted. Please try again.';
    }

    // Default Firestore error message
    return 'An error occurred while accessing the database. Please try again.';
  }

  // Storage error messages
  static String handleStorageError(dynamic error) {
    debugPrint('Storage error: $error');
    final errorString = error.toString().toLowerCase();

    // Permission issues
    if (errorString.contains('unauthorized') || errorString.contains('not-authorized')) {
      return 'You don\'t have permission to upload files.';
    }

    // User interaction issues
    else if (errorString.contains('canceled')) {
      return 'File upload was canceled.';
    }

    // Quota issues
    else if (errorString.contains('quota-exceeded') || errorString.contains('storage/quota-exceeded')) {
      return 'Storage quota exceeded. Please contact support.';
    }

    // File issues
    else if (errorString.contains('invalid-file')) {
      return 'The selected file is invalid. Please choose another file.';
    } else if (errorString.contains('file-not-found')) {
      return 'The requested file does not exist.';
    } else if (errorString.contains('file-too-large')) {
      return 'The file is too large. Please select a smaller file.';
    }

    // Network issues
    else if (errorString.contains('network-error')) {
      return 'Network error occurred during upload. Please check your connection.';
    } else if (errorString.contains('server-error')) {
      return 'Server error occurred. Please try again later.';
    }

    // Retry issues
    else if (errorString.contains('retry-limit-exceeded')) {
      return 'Upload failed after multiple attempts. Please try again.';
    }

    // Default storage error message
    return 'Failed to upload file. Please try again.';
  }

  // Function-related error messages
  static String handleFunctionsError(dynamic error) {
    debugPrint('Functions error: $error');
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('not-found')) {
      return 'The requested function was not found.';
    } else if (errorString.contains('permission-denied')) {
      return 'You don\'t have permission to execute this function.';
    } else if (errorString.contains('unauthenticated')) {
      return 'You need to be signed in to perform this action.';
    } else if (errorString.contains('unavailable')) {
      return 'The service is currently unavailable. Please try again later.';
    } else if (errorString.contains('internal')) {
      return 'An internal error occurred. Please try again later.';
    } else if (errorString.contains('data-loss')) {
      return 'Data loss occurred during the operation.';
    }

    return 'Error executing function. Please try again.';
  }

  // Validation error handler - NEW
  static String handleValidationError(dynamic error) {
    debugPrint('Validation error: $error');
    final errorString = error.toString();

    // For validation errors, just return the original message
    // as it's already user-friendly
    if (errorString.contains('Please select') ||
        errorString.contains('Please provide') ||
        errorString.contains('required') ||
        errorString.contains('cannot be empty') ||
        errorString.contains('at least') ||
        errorString.toLowerCase().contains('missing')) {
      return errorString;
    }

    // Default message if it doesn't match validation patterns
    return 'Please check your input and try again.';
  }

  // General error handler
  static String handleGenericError(dynamic error) {
    debugPrint('Error: $error');
    final errorString = error.toString().toLowerCase();

    // First check if this is a validation error
    if (errorString.contains('please select') ||
        errorString.contains('please provide') ||
        errorString.contains('required') ||
        errorString.contains('cannot be empty') ||
        errorString.contains('at least') ||
        errorString.contains('missing')) {
      return error.toString(); // Return validation errors as-is
    }

    // General app errors
    if (errorString.contains('assertion') || errorString.contains('format')) {
      return 'The app encountered an internal error. Please try again.';
    }

    // Network related general errors
    else if (errorString.contains('network') || errorString.contains('socket') || errorString.contains('connect')) {
      return 'Network connection issue. Please check your internet and try again.';
    }

    // Timeout errors
    else if (errorString.contains('timeout')) {
      return 'The operation timed out. Please try again.';
    }

    // Permission errors
    else if (errorString.contains('permission')) {
      return 'Permission denied. You may not have the right access.';
    }

    // Default error message
    return 'Something went wrong. Please try again.';
  }

  // Show error snackbar using the provider
  static void showErrorSnackBar(WidgetRef ref, dynamic error, {String? context}) {
    String userMessage;
    final errorString = error.toString().toLowerCase();

    // Try to detect the type of error for appropriate handling
    if (errorString.contains('firebase_auth') || errorString.contains('auth/')) {
      userMessage = handleAuthError(error);
    } else if (errorString.contains('firestore') || errorString.contains('cloud_firestore')) {
      userMessage = handleFirestoreError(error);
    } else if (errorString.contains('storage') || errorString.contains('firebase_storage')) {
      userMessage = handleStorageError(error);
    } else if (errorString.contains('functions') || errorString.contains('firebase_functions')) {
      userMessage = handleFunctionsError(error);
    } else if (errorString.contains('please select') ||
        errorString.contains('please provide') ||
        errorString.contains('required') ||
        errorString.contains('cannot be empty')) {
      // Handle validation errors specially to preserve their messages
      userMessage = handleValidationError(error);
    } else {
      userMessage = handleGenericError(error);
    }

    // Add context if provided
    if (context != null) {
      userMessage = '$context: $userMessage';
    }

    showSnackBarRef(ref, userMessage, Colors.red[700]);
  }

  // Show success snackbar
  static void showSuccessSnackBar(WidgetRef ref, String message) {
    showSnackBarRef(ref, message, Colors.green[700]);
  }

  // Show info snackbar
  static void showInfoSnackBar(WidgetRef ref, String message) {
    showSnackBarRef(ref, message, Colors.blue[700]);
  }

  // Show warning snackbar
  static void showWarningSnackBar(WidgetRef ref, String message) {
    showSnackBarRef(ref, message, Colors.orange[700]);
  }
}
