import 'package:entities/timestamp_converter.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:entities/test_result.dart';

part 'test_progress.freezed.dart';
part 'test_progress.g.dart';

/// Enum to represent the different types of answer filters
@JsonEnum(fieldRename: FieldRename.snake)
enum AnswerFilterType {
  incorrect,
  correct,
  skipped,
}

/// Model representing the current state of a test being taken
@Freezed(toJson: true, )
class TestProgress with _$TestProgress {
  factory TestProgress({
    /// Current question index
    required int currentIndex,

    /// Map of question index to selected answer index
    required Map<int, int> userAnswers,

    /// When the test was started
    @TimestampConverter() required DateTime startTime,

    /// Whether the test is completed
    @Default(false) bool isCompleted,

    /// Set of question indices marked for review (to revisit during the test)
    @SetConverter<int>() @Default({}) Set<int> markedForReview,

    /// Set of question indices that are bookmarked (for later review outside the test)
    @SetConverter<int>() @Default({}) Set<int> bookmarkedQuestions,

    /// List indicating the correctness status of each answer
    @Default([]) List<AnswerFilterType> answersCorrectness,

    /// Test result once the test is completed
    TestResult? result,

    /// Set of question indices where the answer has been revealed
    @SetConverter<int>() @Default({}) Set<int> revealedAnswers,

  }) = _TestProgress;

  factory TestProgress.fromJson(Map<String, dynamic> json) => _$TestProgressFromJson(json);
}