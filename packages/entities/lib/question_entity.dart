// packages/entities/lib/question_entity.dart

import 'package:entities/entity.dart';
import 'package:entities/timestamp_converter.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'question_entity.freezed.dart';
part 'question_entity.g.dart';

@JsonEnum(fieldRename: FieldRename.snake)
enum QuestionStatus { draft, review, published }

@JsonEnum(fieldRename: FieldRename.snake)
enum QuestionType { mcq, flipCard }

@JsonEnum(fieldRename: FieldRename.snake)
enum QuestionDifficulty { easy, medium, hard }

@freezed
class QuestionEntity extends Entity with _$QuestionEntity {
  factory QuestionEntity({
    String? id,
    String? questionImage,
    double? questionImageWidth,
    double? questionImageHeight,
    String? answer,
    String? answerImage,
    double? answerImageWidth,
    double? answerImageHeight,
    String? explanation,
    String? reference,
    @TimestampConverter() DateTime? createdAt,
    @TimestampConverter() DateTime? updatedAt,
    required String courseId,
    required String yearId,
    required String subjectId,
    required String question,
    required QuestionType type,
    @Default([]) List<String> options,
    @Default(0) int correctOptionIndex,
    @Default([]) List<String> optionImages,
    @Default([]) List<double> optionImagesWidth,
    @Default([]) List<double> optionImagesHeight,
    @Default([]) List<String> keywords,
    @Default(QuestionDifficulty.easy) QuestionDifficulty difficulty,
    @Default(QuestionStatus.draft) QuestionStatus status,
  }) = _QuestionEntity;

  @override
  factory QuestionEntity.fromJson(Map<String, dynamic> json) => reportAnyJsonBugs(_$QuestionEntityFromJson, json);
}
