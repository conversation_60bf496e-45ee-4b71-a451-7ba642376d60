// packages/providers/lib/test_provider.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/question_entity.dart';
import 'package:entities/test_entity.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'test_provider.g.dart';

@riverpod
class TestRepository extends _$TestRepository {
  @override
  FutureOr<void> build() {}

  Future<List<TestEntity>> getTests({
    String? courseId,
    String? yearId,
    String? subjectId,
    String? type,
    String? difficulty,
    String? status,
  }) async {
    Query query = FirebaseFirestore.instance.collection('tests');

    if (courseId != null) query = query.where('courseId', isEqualTo: courseId);
    if (yearId != null) query = query.where('yearId', isEqualTo: yearId);
    if (subjectId != null) {
      query = query.where('subjectId', isEqualTo: subjectId);
    }
    if (type != null) query = query.where('type', isEqualTo: type);
    if (difficulty != null) {
      query = query.where('difficulty', isEqualTo: difficulty);
    }
    if (status != null) query = query.where('status', isEqualTo: status);

    final querySnapshot = await query.get();
    return querySnapshot.docs
        .map((doc) => TestEntity.fromJson({...doc.data() as Map<String, dynamic>, 'id': doc.id}))
        .toList();
  }

  Future<String> addTest(TestEntity test) async {
    final String id = test.id ?? FirebaseFirestore.instance.collection('tests').doc().id;

    final docRef = FirebaseFirestore.instance.collection('tests').doc(id);

    final data = test.copyWith(id: id).toJson();

    final now = DateTime.now();
    data['createdAt'] = now.toIso8601String();
    data['updatedAt'] = now.toIso8601String();

    await docRef.set(data);
    return id;
  }

  Future<void> updateTest(TestEntity test) async {
    if (test.id == null) {
      throw Exception('Cannot update test without ID');
    }

    final data = test.toJson();
    data.remove('id');
    data['updatedAt'] = DateTime.now().toIso8601String();

    await FirebaseFirestore.instance.collection('tests').doc(test.id).update(data);
  }

  Future<void> publishTest(String testId) async {
    final docRef = FirebaseFirestore.instance.collection('tests').doc(testId);
    final testDoc = await docRef.get();

    if (!testDoc.exists) {
      throw Exception('Test not found');
    }

    await docRef.update({'status': QuestionStatus.published.name, 'updatedAt': DateTime.now().toIso8601String()});
  }

  Future<void> deleteTest(String testId) async {
    await FirebaseFirestore.instance.collection('tests').doc(testId).delete();
  }
}

@riverpod
Stream<List<TestEntity>> filteredTests(
  Ref ref, {
  String? courseId,
  String? yearId,
  String? subjectId,
  String? type,
  QuestionDifficulty? difficulty,
  QuestionStatus? status,
}) {
  return FirebaseFirestore.instance
      .collection('tests')
      .where('courseId', isEqualTo: courseId)
      .where('yearId', isEqualTo: yearId)
      .where('subjectId', isEqualTo: subjectId)
      .where('type', isEqualTo: type)
      .where('difficulty', isEqualTo: difficulty?.name)
      .where('status', isEqualTo: status?.name)
      .snapshots()
      .map((snapshot) => snapshot.docs
          .map((doc) => TestEntity.fromJson({
                ...doc.data(),
                'id': doc.id,
              }))
          .toList());
}
