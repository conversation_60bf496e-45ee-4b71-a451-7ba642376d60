rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    match /app_config/{document=**} {
      allow read: if request.auth != null;
    }

		match /published_free/{document=**} {
      allow get: if isAuthenticated();
    }

    match /published_test/{subscriptionId}/{document=**} {
      allow get: if isAuthenticated() && hasValidSubscription(subscriptionId)
    }

    // Users collection rules
    match /users/{userId} {
      // Admin has full access
      allow read, write: if isAdmin();

      // Users can read their own document
      allow get: if isUser(userId);

      // Users can create or update their own document if not modifying subscriptions,
      // only using allowed fields, and ensuring the userId field matches the document ID
      allow create, update: if isUser(userId) && hasOnlyAllowedFields();

      // No one can delete user documents (except admin via the rule above)
      allow delete: if false;
    }

    match /users/{userId}/attempts/{attemptId} {
      // Admin has full access
      allow read, write: if isAdmin();

      // Users can read their own document
      allow get: if isUser(userId);

      // Users can read and write their own document
      allow create, update: if isUser(userId);

    }

    // admin may do everything for the time being
    match /{document=**} {
      allow read, write: if request.auth.token.isAdmin;
    }

    // deny everything else
    match /{document=**} {
      allow read, write: if false
    }

    // Check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Check if user has admin role
    function isAdmin() {
      return request.auth.token.isAdmin == true;
    }

    // Check if user is modifying their own document
    function isUser(userId) {
      return request.auth.uid == userId;
    }

    // Check if uid field matches document ID
    function ensureUidMatchesDocId(docId) {
      return !('uid' in request.resource.data) || request.resource.data.uid == docId;
    }

    // Check if request only contains allowed fields
    function hasOnlyAllowedFields() {
      let allowedFields = ['displayName', 'signedUp', 'email', 'provider', 'uid', 'courseId', 'yearId', 'bookmarks', 'results'];
      // For new documents we need to check request.resource.data.keys.
      // We do not check for non-existence since this is an extra query,
      // it is good enough to check if the final result is allowed since there are no values to be merged.
      let isNewAndAllowed =  request.resource.data.keys().hasOnly(allowedFields);
      // for existing documents we need to check request.resource.data.diff(resource.data).affectedKeys()
      let existsAndAllowed = request.resource.data.diff(resource.data).affectedKeys().hasOnly(allowedFields);
      return isNewAndAllowed || existsAndAllowed;
    }

    // Check if user has subscription for this item and it's not expired
    function hasValidSubscription(subscriptionId) {
      let userId = request.auth.uid;
      let userDoc = get(/databases/$(database)/documents/users/$(userId));
      let subscriptions = userDoc.data.subscriptions;

      return subscriptions != null
             && subscriptionId in subscriptions
             && subscriptions[subscriptionId] > request.time;
    }
  }
}