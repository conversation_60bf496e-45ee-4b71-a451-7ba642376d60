rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // ADMIN OVERRIDE - Must come first to ensure admin access
    // Simple 2-role system: 'admin' and 'user'
    match /{document=**} {
      allow read, write, delete: if hasAdminAccess();
    }

    match /course_catalog/{document=**} {
      allow read: if request.auth != null;
    }

		match /published_free/{document=**} {
      allow get: if request.auth != null;
    }

    match /published_test/{document=**} {
      allow get: if request.auth != null && hasValidSubscription(request.auth.uid);
    }

    // Users collection rules
    match /users/{userId} {
      // Users can read their own document (ownership check)
      allow get: if isOwner(userId);

      // Users can create or update their own document if not modifying subscriptions,
      // only using allowed fields, and ensuring the userId field matches the document ID
      allow create, update: if isOwner(userId) && hasOnlyAllowedFields();

      // Regular users cannot delete their own documents
      allow delete: if false;
    }

    match /users/{userId}/attempts/{attemptId} {
      // Users can read their own attempts (ownership check)
      allow get: if isOwner(userId);

      // Users can read and write their own attempts (ownership check)
      allow create, update: if isOwner(userId);
    }

    // User claims collection - critical for role management
    match /user_claims/{userId} {
      // Only admin can read user claims (for user management)
      allow read: if hasAdminAccess();

      // Only admin can create/update user claims
      allow create, update: if hasAdminAccess();

      // Only admin can delete user claims
      allow delete: if hasAdminAccess();
    }

    // Questions collection - content management
    match /questions/{questionId} {
      // All authenticated users can read questions
      allow read: if isAuthenticated();

      // Only admin can create questions
      allow create: if hasAdminAccess();

      // Only admin can edit questions
      allow update: if hasAdminAccess();

      // Only admin can delete questions
      allow delete: if hasAdminAccess();
    }

    // Courses collection - course management
    match /courses/{courseId} {
      // All authenticated users can read courses
      allow read: if isAuthenticated();

      // Only admin can manage courses
      allow create, update, delete: if hasAdminAccess();
    }

    // Tests collection - test management
    match /tests/{testId} {
      // All authenticated users can read tests
      allow read: if isAuthenticated();

      // Only admin can create tests
      allow create: if hasAdminAccess();

      // Only admin can edit tests
      allow update: if hasAdminAccess();

      // Only admin can delete tests
      allow delete: if hasAdminAccess();
    }

    // Check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Check if user has admin access (simple admin role)
    function hasAdminAccess() {
      return hasAdminRole();
    }



    // Check if user is modifying their own document (ownership check)
    function isOwner(userId) {
      return request.auth.uid == userId;
    }




    // Check if request only contains allowed fields
    function hasOnlyAllowedFields() {
      let allowedFields = ['displayName', 'signedUp', 'email', 'provider', 'uid', 'courseId', 'yearId', 'bookmarks', 'results', 'role', 'roleAssignedAt', 'roleAssignedBy', 'isActive', 'lastLoginAt', 'subscriptions', 'transactions'];
      // For new documents we need to check request.resource.data.keys.
      // We do not check for non-existence since this is an extra query,
      // it is good enough to check if the final result is allowed since there are no values to be merged.
      let isNewAndAllowed =  request.resource.data.keys().hasOnly(allowedFields);
      // for existing documents we need to check request.resource.data.diff(resource.data).affectedKeys()
      let existsAndAllowed = request.resource.data.diff(resource.data).affectedKeys().hasOnly(allowedFields);
      return isNewAndAllowed || existsAndAllowed;
    }

    // Check if user has subscription for this item and it's not expired
    function hasValidSubscription(userId) {
      let userDoc = get(/databases/$(database)/documents/users/$(userId));
      let subscriptions = userDoc.data.subscriptions;
      let subscriptionId = resource.data.subscriptionId;

      return subscriptions != null
             && subscriptionId in subscriptions
             && subscriptions[subscriptionId].expiryTime > request.time;
    }

    // Simple 2-role system functions (role checks)
    function hasAdminRole() {
      return request.auth != null && request.auth.token.role == 'admin';
    }
  }
}